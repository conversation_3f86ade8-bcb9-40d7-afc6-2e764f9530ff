package com.yeepay.spacetravel.service.core.aspect;

import com.yeepay.spacetravel.common.util.config.ConfigUtils;
import com.yeepay.spacetravel.common.util.exception.ReturnCode;
import com.yeepay.spacetravel.service.core.annotation.UnifiedProcessReturnValueAnnotation;
import com.yeepay.spacetravel.service.facade.result.BaseResult;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.collections.MapUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: qingwang.gu
 * @Date: 2022/02/24 4:31 下午
 * @Description:
 */
@Component
@Aspect
public class UnifiedProcessReturnValueAspect {
    private Logger logger = LoggerFactory.getLogger(UnifiedProcessReturnValueAspect.class);

    @AfterReturning(pointcut = "@annotation(unifiedProcessReturnValueAnnotation)",
            returning="o")
    public void after(JoinPoint pjp, UnifiedProcessReturnValueAnnotation unifiedProcessReturnValueAnnotation, Object o) throws Throwable {
        try{
            if(o instanceof BaseResult){
                BaseResult baseResult = (BaseResult) o;
                if(!baseResult.getReturnCode().equals(ReturnCode.SUCCESS)){
                    String message = baseResult.getReturnMessage();
                    logger.info("系统的返回错误信息为{}",message);
                    if(!CheckUtils.isEmpty(message)){
                        Map<String,String> map = ConfigUtils.getDefaultValue("SPACE_TRAVEL_DEAL_RETURN_MESSAGE_KEY",new HashMap<>());
                        if(MapUtils.isNotEmpty(map)){
                            String unifiedErrorMessage = ConfigUtils.getDefaultValue("SPACE_TRAVEL_DEAL_RETURN_MESSAGE_INFO","系统开小差稍后重试");
                            for(String key : map.keySet()){
                                logger.info("统一配置中为异常统一处理设置的key为{}",key);
                                String  stackTrace= map.get(key);
                                if(message.trim().startsWith(key)||message.contains(key)){
                                    if(CheckUtils.isEmpty(stackTrace)||"".equals(stackTrace)){
                                        message = unifiedErrorMessage;
                                    }else{
                                        message = stackTrace;
                                    }

                                    baseResult.setReturnMessage(message);
                                    logger.info("将错误信息统一修改为{}",message);
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            logger.error("在给返回值做aop的时候出现位置异常",e);
        }
    }
}
