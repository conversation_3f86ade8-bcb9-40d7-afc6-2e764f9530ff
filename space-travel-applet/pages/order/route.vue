<script>
import { PRICE_TYPE, PRICE_TYPE_ZH } from '@/utils/type'
import RouteCellMini from '@/components/route-cell-mini/route-cell-mini.vue'
import RouteCell from '@/components/route-cell/route-cell.vue'

export default {
  components: {
    RouteCellMini,
    RouteCell
  },
  data() {
    return {
      showStartDate: false,
      startDate: '',
      calendarPriceList: [],
      current: 0,
      status: 'loadmore',
      list: [], // 单程航班列表
      searchForm: {
        adultNum: 0,
        childNum: 0,
        startDate: '',
        endDate: '',
        startCity: '',
        endCity: '',
        peopleType: {},
        journeyType: 'OW', // OW-单程, RT-往返
      },
      monthNum: 3,
      marginTop: 0,
      menuButtonInfo: null, // 胶囊按钮信息
      // 往返程相关状态
      isRoundTrip: false,
      currentStep: 'departure', // 当前步骤：departure-去程, return-回程
      selectedDeparture: null, // 选中的去程航班
      selectedReturn: null, // 选中的回程航班
      departureList: [], // 去程航班列表
      returnList: [], // 回程航班列表
      departureStatus: 'loadmore', // 去程加载状态
      returnStatus: 'loadmore', // 回程加载状态
      loading: false, // 总体加载状态
      departureDateList: [],
      returnDateList: [],
      departureCurrent: 0,
      returnCurrent: 0,
      showDepartureCalendar: false,
      showReturnCalendar: false,
      selectedCabinType: 'economy', // 用户选择的舱位类型：economy-经济舱, business-头等/公务舱
      priceType: 'EVERY_DISCOUNT', // 优惠类型，从上级页面传入
    }
  },
  onLoad({ data }) {
    try {
      // 获取窗口信息
      const windowInfo = uni.getWindowInfo()
      // 获取胶囊按钮信息
      this.menuButtonInfo = uni.getMenuButtonBoundingClientRect()
      
      // 计算顶部安全区域高度
      this.marginTop = Number(windowInfo.statusBarHeight) + Number(windowInfo.safeAreaInsets.top)
      
      // 解析搜索参数（先进行URL解码）
      this.searchForm = JSON.parse(decodeURIComponent(data))
      
      // 设置优惠类型
      this.priceType = this.searchForm.selectedPriceType || 'EVERY_DISCOUNT'
      
      // 判断是否为往返程
      this.isRoundTrip = this.searchForm.journeyType === 'RT'
      
      console.log('页面初始化 - 当前模式:', this.isRoundTrip ? '往返程' : '单程')
      console.log('搜索参数:', this.searchForm)
      console.log('优惠类型:', this.priceType)
      
      // 初始化日历和航班数据
      this.getCalendarDate()
      this.refresh()
    } catch (error) {
      console.error('页面初始化错误:', error)
      uni.showToast({
        title: '页面初始化失败，请重试',
        icon: 'none'
      })
    }
  },

  computed: {
    getTopWidth() {
      if (!this.menuButtonInfo) {
        return { width: 0, right: 0 }
      }
      
      const screen = uni.getWindowInfo()
      const right = (screen.screenWidth - this.menuButtonInfo.right) + this.menuButtonInfo.width + 11 - 50
      const width = screen.screenWidth - ((screen.screenWidth - this.menuButtonInfo.right) + this.menuButtonInfo.width) - 50 - 11
      
      return {
        width,
        right,
      }
    },
    // 往返程总价
    totalPrice() {
      if (!this.isRoundTrip || !this.selectedDeparture || !this.selectedReturn) return 0
      
      const departurePrice = this.calculateFlightPrice(this.selectedDeparture)
      const returnPrice = this.calculateFlightPrice(this.selectedReturn)
      
      return departurePrice + returnPrice
    },
    // 是否可以确认订单
    canConfirmOrder() {
      if (!this.isRoundTrip) {
        return this.selectedDeparture !== null
      }
      return this.selectedDeparture !== null && this.selectedReturn !== null
    },

  },
  methods: {
    refresh() {
      console.log('开始刷新航班数据 - 当前模式:', this.isRoundTrip ? '往返程' : '单程')
      
              if (this.isRoundTrip) {
          // 往返程：先加载去程，成功后再加载回程
          console.log('往返程模式 - 开始加载去程航班')
          this.loading = true
          this.departureList = []
          this.returnList = []
          this.departureStatus = 'loadmore'
          this.returnStatus = 'loadmore'
          
          // 串行加载：先去程，后回程
          this.loadDepartureFlights()
            .then(() => {
              // 去程加载成功后，加载回程
              if (this.departureList.length > 0) {
                console.log('去程加载成功，开始加载回程航班')
                return this.loadReturnFlights()
              } else {
                console.warn('去程航班列表为空，设置回程状态为nomore')
                this.returnList = []
                this.returnStatus = 'nomore'
                // 返回一个resolved的Promise，确保finally能正常执行
                return Promise.resolve()
              }
            })
            .catch(error => {
              console.error('去程航班加载失败:', error)
              // 确保回程状态也被设置
              this.returnList = []
              this.returnStatus = 'nomore'
            })
            .finally(() => {
              this.loading = false
              console.log('往返程航班加载完成')
              console.log('最终状态 - departureList长度:', this.departureList.length, 'returnList长度:', this.returnList.length)
              console.log('最终状态 - departureStatus:', this.departureStatus, 'returnStatus:', this.returnStatus)
            })
      } else {
        // 单程：直接查询航班
        console.log('单程模式 - 开始查询航班')
        this.status = 'loadmore'
        this.list = []
        this.queryFlights()
      }
    },
    
    /**
     * 加载去程航班
     */
    async loadDepartureFlights() {
      console.log('开始加载去程航班 - loadDepartureFlights 被调用')
      this.departureStatus = 'loading'
      uni.showLoading({ 
        title: '正在加载航班...',
        mask: false
      })
      
      try {
        // 构建去程查询参数
        const baseParams = {
          adultNum: this.searchForm.peopleType.adult || 1,
          childNum: this.searchForm.peopleType.child || 0,
          journeyType: this.isRoundTrip ? 'RT' : 'OW',
          deptCityCode: this.searchForm.startCityCode,
          arrCityCode: this.searchForm.endCityCode,
          deptAirport: this.searchForm.startCityAirport || '',
          arrAirport: this.searchForm.endCityAirport || '',
          deptDate: this.searchForm.startDate,
          priceType: this.priceType // 使用动态的优惠类型参数
        }

        // 往返程时添加journeyList
        const params = this.isRoundTrip ? {
          ...baseParams,
          journeyList: [
            {
              deptAirportCode: this.searchForm.startCityAirport || this.searchForm.startCityCode,
              arrAirportCode: this.searchForm.endCityAirport || this.searchForm.endCityCode,
              deptDate: this.searchForm.startDate
            },
            {
              deptAirportCode: this.searchForm.endCityAirport || this.searchForm.endCityCode,
              arrAirportCode: this.searchForm.startCityAirport || this.searchForm.startCityCode,
              deptDate: this.searchForm.endDate
            }
          ]
        } : baseParams
        
        console.log('加载去程航班参数:', params)
        
        let result
        if (this.isRoundTrip) {
          try {
            // 尝试使用NDC往返程接口
            result = await this.$Api.queryDepartureFlight(params)
          } catch (ndcError) {
            console.warn('NDC往返程查询失败，降级使用普通查询:', ndcError)
            // 降级使用普通单程查询
            result = await this.$Api.queryFlight({
              ...baseParams,
              journeyType: 'OW' // 降级时使用单程查询
            })
          }
        } else {
          // 单程直接使用普通查询
          result = await this.$Api.queryFlight(params)
        }
        
        // 日志输出
        console.log('去程航班接口返回结果:', result)
        
        // 兼容不同的返回格式
        let flightData = null
        if (result && result.success && result.data && Array.isArray(result.data)) {
          // 格式1: {success: true, data: [...]}
          console.log('检测到包装格式数据，使用 result.data')
          flightData = result.data
        } else if (result && Array.isArray(result)) {
          // 格式2: 直接返回数组 [...]
          console.log('检测到直接数组格式数据，使用 result')
          flightData = result
        } else {
          console.log('未识别的数据格式:', typeof result, result)
        }
        
        if (flightData && flightData.length > 0) {
          this.departureList = flightData
          this.departureStatus = 'nomore'
          console.log('✅ 去程航班加载成功:', flightData.length, '个航班')
          console.log('✅ 第一个航班数据:', flightData[0])
        } else {
          this.departureList = []
          this.departureStatus = 'nomore'
          const errorMsg = (result && result.message) || '暂无可用航班'
          console.error('去程航班查询失败:', errorMsg)
          uni.showToast({
            title: errorMsg,
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('去程航班查询异常:', error && error.message || error)
        this.departureList = []
        this.departureStatus = 'nomore'
        uni.showToast({
          title: error && error.returnMessage || '查询失败，请稍后重试',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },
    
    /**
     * 加载回程航班
     */
    async loadReturnFlights() {
      if (!this.isRoundTrip) return
      
      console.log('开始加载回程航班 - loadReturnFlights 被调用')
      this.returnStatus = 'loading'
      
      try {
        // 确定使用的去程航班：用户选择的 > 默认第一条 > 空
        let departureFlight = this.selectedDeparture
        if (!departureFlight && this.departureList.length > 0) {
          departureFlight = this.departureList[0]
          // 设置第一个去程航班为默认选中状态
          this.selectedDeparture = departureFlight
          console.log('使用默认去程航班:', departureFlight.flightBaseInfoVo?.flightNo)
        }
        
        // 🔍 调试去程航班数据结构
        if (departureFlight) {
          console.log('🔍 去程航班数据结构检查:')
          console.log('   - 航班号:', departureFlight.flightBaseInfoVo?.flightNo)
          console.log('   - 舱位数量:', departureFlight.cabinClses?.length || 0)
          if (departureFlight.cabinClses && departureFlight.cabinClses.length > 0) {
            console.log('   - 第一个舱位数据:', departureFlight.cabinClses[0])
            console.log('   - cabinPriceId 存在?', !!departureFlight.cabinClses[0].cabinPriceId)
            console.log('   - cabinPriceItemId 存在?', !!departureFlight.cabinClses[0].cabinPriceItemId)
          }
        }
        
        if (!departureFlight) {
          console.error('没有可用的去程航班信息，无法查询回程')
          this.returnList = []
          this.returnStatus = 'nomore'
          return
        }
        
        // 构建回程查询参数 - 匹配后端 QueryReturnFlightParam 结构
        const baseParams = {
          adultNum: this.searchForm.peopleType.adult || 1,
          childNum: this.searchForm.peopleType.child || 0,
          journeyType: 'RT',
          // 后端期望的字段名
          deptAirport: this.searchForm.endCityAirport || this.searchForm.endCityCode || '',
          arrAirport: this.searchForm.startCityAirport || this.searchForm.startCityCode || '',
          returnDate: this.searchForm.endDate,
          priceType: this.priceType, // 使用动态的优惠类型参数
          // 去程报价信息 - 只传递默认选择的舱位，而不是所有舱位
          offer: departureFlight.cabinClses && departureFlight.cabinClses.length > 0 ? 
            (() => {
              // 选择默认舱位：优先选择用户当前查看类型的舱位，否则选择最便宜的舱位
              let selectedCabin = null
              
              // 根据用户选择的舱位类型过滤
              const currentCabinType = this.selectedCabinType || 'economy'
              const filteredCabins = departureFlight.cabinClses.filter(cabin => {
                const cabinCode = (cabin.cabinCls || cabin.cabinCode || '').toUpperCase()
                if (currentCabinType === 'economy') {
                  return ['Y', 'M', 'H', 'K', 'L', 'Q', 'T', 'E', 'U', 'V', 'W', 'S', 'N', 'R', 'G', 'X', 'B'].includes(cabinCode)
                } else if (currentCabinType === 'business') {
                  return ['F', 'C', 'J', 'D', 'I', 'Z', 'P', 'A', 'O'].includes(cabinCode)
                }
                return true
              })
              
              // 从中选择最便宜的
              if (filteredCabins.length > 0) {
                selectedCabin = filteredCabins.reduce((min, cabin) => {
                  const currentPrice = parseFloat(cabin.priceAdult || cabin.adultOrigPrice || 0) + parseFloat(cabin.taxFeeAdult || 0)
                  const minPrice = parseFloat(min.priceAdult || min.adultOrigPrice || 0) + parseFloat(min.taxFeeAdult || 0)
                  return currentPrice < minPrice ? cabin : min
                })
              } else {
                // 如果当前类型没有舱位，选择最便宜的舱位
                selectedCabin = departureFlight.cabinClses.reduce((min, cabin) => {
                  const currentPrice = parseFloat(cabin.priceAdult || cabin.adultOrigPrice || 0) + parseFloat(cabin.taxFeeAdult || 0)
                  const minPrice = parseFloat(min.priceAdult || min.adultOrigPrice || 0) + parseFloat(min.taxFeeAdult || 0)
                  return currentPrice < minPrice ? cabin : min
                })
              }
              
              // 根据乘客信息确定使用的cabinPriceItemId
              let cabinPriceItemId = ''
              const adultNum = this.searchForm.peopleType.adult || 1
              const childNum = this.searchForm.peopleType.child || 0
              
              if (selectedCabin.adultCabinPriceItemId && selectedCabin.childCabinPriceItemId) {
                if (childNum > 0) {
                  cabinPriceItemId = selectedCabin.childCabinPriceItemId
                } else {
                  cabinPriceItemId = selectedCabin.adultCabinPriceItemId
                }
              } else {
                cabinPriceItemId = selectedCabin.adultCabinPriceItemId || selectedCabin.cabinPriceItemId || ''
              }
              
              console.log(`选择默认舱位${selectedCabin.cabinCls}: 成人数=${adultNum}, 儿童数=${childNum}, 最终使用=${cabinPriceItemId}`)
              
              return [{
                cabinPriceId: selectedCabin.cabinPriceId || '',
                cabinPriceItemId: cabinPriceItemId
              }]
            })() : []
        }

        // 直接使用 baseParams，不添加 journeyList（后端 QueryReturnFlightParam 中没有此字段）
        const params = baseParams
        
        console.log('🚀 最终发送给后端的回程查询参数 (匹配 QueryReturnFlightParam):', JSON.stringify(params, null, 2))
        console.log('🚀 关键参数检查 (后端字段名):')
        console.log('   - 回程出发机场(deptAirport):', params.deptAirport)
        console.log('   - 回程到达机场(arrAirport):', params.arrAirport)
        console.log('   - 回程日期(returnDate):', params.returnDate)
        console.log('   - 去程报价信息(offer)数量:', params.offer && params.offer.length, '个报价')
        console.log('   - offer详情:', params.offer)
        console.log('   - 成人数(adultNum):', params.adultNum)
        console.log('   - 儿童数(childNum):', params.childNum)
        console.log('   - 行程类型(journeyType):', params.journeyType)
        
        // 直接调用回程查询接口，后端已有降级逻辑
        let result = await this.$Api.queryReturnFlight(params)
        
        console.log('回程航班接口返回结果:', result)
        
        // 兼容不同的返回格式
        let flightData = null
        if (result && result.success && result.data && Array.isArray(result.data)) {
          // 格式1: {success: true, data: [...]}
          console.log('回程：检测到包装格式数据，使用 result.data')
          flightData = result.data
        } else if (result && Array.isArray(result)) {
          // 格式2: 直接返回数组 [...]
          console.log('回程：检测到直接数组格式数据，使用 result')
          flightData = result
        } else {
          console.log('回程：未识别的数据格式:', typeof result, result)
        }
        
        if (flightData && flightData.length > 0) {
          this.returnList = flightData
          this.returnStatus = 'nomore'
          console.log('✅ 回程航班加载成功:', flightData.length, '个航班')
        } else {
          this.returnList = []
          this.returnStatus = 'nomore'
          const errorMsg = (result && result.message) || '暂无可用航班'
          console.error('回程航班查询失败:', errorMsg)
          uni.showToast({
            title: errorMsg,
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('回程航班查询异常:', error && error.message || error)
        this.returnList = []
        this.returnStatus = 'nomore'
        uni.showToast({
          title: error && error.returnMessage || '查询失败，请稍后重试',
          icon: 'none'
        })
      } finally {
        // 确保loading状态被清除
        if (this.returnStatus === 'loading') {
          console.warn('回程航班加载完成但状态仍为loading，强制设置为nomore')
          this.returnStatus = 'nomore'
        }
        console.log('回程航班加载完成 - 最终状态:', this.returnStatus, '数据量:', this.returnList.length)
      }
    },
    
    /**
     * 单程航班查询（保持原有逻辑）
     */
    queryFlights() {
      if (this.status === 'loading' || this.status === 'nomore') return
      
      this.status = 'loading'
      uni.showLoading({ 
        title: '正在查询航班...',
        mask: false
      })
      
      const params = {
        adultNum: this.searchForm.peopleType.adult || 1,
        childNum: this.searchForm.peopleType.child || 0,
        journeyType: this.searchForm.journeyType,
        deptCityCode: this.searchForm.startCityCode,
        arrCityCode: this.searchForm.endCityCode,
        deptAirport: this.searchForm.startCityAirport || '',
        arrAirport: this.searchForm.endCityAirport || '',
        deptDate: this.searchForm.startDate,
        priceType: this.priceType // 使用动态的优惠类型参数
      }
      
      console.log('单程航班查询参数:', params)
      
      this.$Api.queryFlight(params).then(result => {
        if (result && result.length > 0) {
          this.list = result
          this.status = 'nomore'
          console.log('单程航班加载成功:', result.length, '个航班')
        } else {
          this.list = []
          this.status = 'nomore'
          const errorMsg = '暂无可用航班'
          console.error('单程航班查询失败:', errorMsg)
          uni.showToast({
            title: errorMsg,
            icon: 'none'
          })
        }
      }).catch(error => {
        console.error('航班查询异常:', error)
        this.list = []
        this.status = 'nomore'
        uni.showToast({
          title: error && error.returnMessage || '查询失败，请稍后重试',
          icon: 'none'
        })
      }).finally(() => {
        uni.hideLoading()
      })
    },
    
    /**
     * 选择航班
     * @param {Object} flightData 航班数据
     * @param {string} type 'departure' | 'return' | 'single'
     */
    async selectFlight(flightData, type) {
      // 添加数据验证
      if (!flightData) {
        console.error('航班数据为空')
        uni.showToast({
          title: '航班数据错误，请重新选择',
          icon: 'none'
        })
        return
      }
      
      if (!this.isRoundTrip) {
        // 单程：直接跳转到舱位选择
        this.toShipSpace(flightData)
        return
      }
      
      // 往返程：选择航班
      if (type === 'departure') {
        this.selectedDeparture = flightData
        console.log('选择去程航班:', flightData.flightBaseInfoVo && flightData.flightBaseInfoVo.flightNo)
        console.log('重新加载回程航班以获取正确的组合价格...')
        // 选择去程航班后重新加载回程航班，以获取正确的组合价格
        await this.loadReturnFlights()
      } else if (type === 'return') {
        this.selectedReturn = flightData
        console.log('选择回程航班:', flightData.flightBaseInfoVo && flightData.flightBaseInfoVo.flightNo)
      }
      
      // 如果两个航班都选择了，可以显示确认按钮
      if (this.selectedDeparture && this.selectedReturn) {
        console.log('往返程航班都已选择，可以确认订单')
      }
    },
    
    /**
     * 计算航班价格（与列表价格保持一致）
     * 计算所有舱位中票价的最小值
     */
    calculateFlightPrice(flightData) {
      if (!flightData || !flightData.cabinClses || !flightData.cabinClses.length) {
        console.log('⚠️ calculateFlightPrice: 没有舱位数据', flightData)
        return 0
      }
      
      console.log('🔍 calculateFlightPrice 价格计算调试:')
      console.log('  - flightData:', flightData)
      console.log('  - cabinClses数量:', flightData.cabinClses.length)
      
      // 先过滤出经济舱舱位（与route-cell-mini.vue保持一致）
      const economyCabins = flightData.cabinClses.filter(cabin => {
        const cabinCode = (cabin.cabinCls || cabin.cabinCode || cabin.cabin || '').toUpperCase()
        // 经济舱代码（与舱位选择页面保持一致）
        return ['Y', 'M', 'H', 'K', 'L', 'Q', 'T', 'E', 'U', 'V', 'W', 'S', 'N', 'R', 'G', 'X', 'B'].includes(cabinCode)
      })
      
      console.log(`  - 经济舱舱位数量: ${economyCabins.length}`)
      
      if (economyCabins.length === 0) {
        console.log('  - 没有经济舱舱位，返回0')
        return 0
      }
      
      // 计算经济舱舱位的票价+税费，取最小值
      const prices = economyCabins.map((cabin, index) => {
        const basePrice = parseFloat(cabin.priceAdult || cabin.adultOrigPrice || 0)
        //const taxFee = parseFloat(cabin.taxFeeAdult || 0)
        const totalPrice = basePrice  // 只计算票价
        
        console.log(`  - 经济舱舱位${index + 1} (${cabin.cabinCode || cabin.cabinCls || cabin.cabin}): 票价=${totalPrice}`)
        return totalPrice
      }).filter(price => price > 0)
      
      const minPrice = prices.length > 0 ? Math.min(...prices) : 0
      console.log(`  - 最终经济舱最低价格(票价): ${minPrice}`)
      
      return minPrice
    },
    
    /**
     * 根据舱位类型获取航班价格
     * @param {Object} flightData 航班数据
     * @param {String} cabinType 舱位类型 economy/business
     * @param {Boolean} onlyTicketPrice 是否仅计算票价含税（不含机建燃油费用）
     */
    getFlightPriceByType(flightData, cabinType, onlyTicketPrice = false) {
      console.log('🔍 route.vue getFlightPriceByType 价格计算调试:')
      console.log('  - flightData:', flightData)
      console.log('  - cabinType:', cabinType)
      console.log('  - onlyTicketPrice:', onlyTicketPrice)
      
      if (!flightData || !flightData.cabinClses || !flightData.cabinClses.length) {
        console.log('  ❌ 没有舱位数据，返回0')
        return 0
      }
      
      console.log('  - 舱位总数:', flightData.cabinClses.length)
      console.log('  - 所有舱位数据:', flightData.cabinClses)
      
      // 根据舱位类型过滤舱位
      const filteredCabins = flightData.cabinClses.filter(cabin => {
        const cabinCode = (cabin.cabinCls || cabin.cabinCode || cabin.cabin || '').toUpperCase()
        if (cabinType === 'economy') {
          return ['Y', 'M', 'H', 'K', 'L', 'Q', 'T', 'E', 'U', 'V', 'W', 'S', 'N', 'R', 'G', 'X', 'B'].includes(cabinCode)
        } else if (cabinType === 'business') {
          return ['F', 'C', 'J', 'D', 'I', 'Z', 'P', 'A', 'O'].includes(cabinCode)
        }
        return true
      })
      
      console.log('  - 过滤后的舱位数量:', filteredCabins.length)
      console.log('  - 过滤后的舱位:', filteredCabins)
      
      if (filteredCabins.length === 0) {
        console.log('  ⚠️ 当前舱位类型没有匹配舱位，使用所有舱位计算最低价')
        // 如果没有匹配的舱位类型，返回最低价格
        const allPrices = flightData.cabinClses.map((cabin, index) => {
          // 优先使用 priceAdult，如果为空则使用 adultOrigPrice
          const basePrice = parseFloat(cabin.priceAdult || cabin.adultOrigPrice || 0)
          const taxFee = parseFloat(cabin.taxFeeAdult || 0)
          
          let price = basePrice
          if (!onlyTicketPrice) {
            price += parseFloat(cabin.oilFeeAdult || 0) + 
                    parseFloat(cabin.amtAdultAirPortFee || 0)
          }
          
          console.log(`    - 全部舱位${index + 1}: ${price} (basePrice: ${basePrice}, taxFee: ${taxFee}, onlyTicketPrice: ${onlyTicketPrice})`)
          return price
        }).filter(price => price > 0)
        
        const minPrice = allPrices.length > 0 ? Math.min(...allPrices) : 0
        console.log(`  - 全部舱位最低价: ${minPrice}`)
        return minPrice
      }
      
      // 计算过滤后舱位的最低价格
      const prices = filteredCabins.map((cabin, index) => {
        // 优先使用 priceAdult，如果为空则使用 adultOrigPrice
        const priceAdult = parseFloat(cabin.priceAdult || cabin.adultOrigPrice || 0)
        const taxFeeAdult = parseFloat(cabin.taxFeeAdult || 0)
        
        let totalPrice = priceAdult + taxFeeAdult // 票价含税
        if (!onlyTicketPrice) {
          const oilFeeAdult = parseFloat(cabin.oilFeeAdult || 0)
          const amtAdultAirPortFee = parseFloat(cabin.amtAdultAirPortFee || 0)
          totalPrice += oilFeeAdult + amtAdultAirPortFee
          
          console.log(`    - 过滤舱位${index + 1} (${cabin.cabinCls || cabin.cabinCode || cabin.cabin}):`)
          console.log(`      * 票价: ${priceAdult}`)
          console.log(`      * 税费: ${taxFeeAdult}`)
          console.log(`      * 燃油: ${oilFeeAdult}`)
          console.log(`      * 机建: ${amtAdultAirPortFee}`)
          console.log(`      * 总价: ${totalPrice}`)
        } else {
          console.log(`    - 过滤舱位${index + 1} (${cabin.cabinCls || cabin.cabinCode || cabin.cabin}): 票价含税 ${totalPrice}`)
        }
        
        return totalPrice
      }).filter(price => price > 0)
      
      const finalPrice = prices.length > 0 ? Math.min(...prices) : 0
      console.log(`  - 最终价格: ${finalPrice}`)
      
      return finalPrice
    },
    
    /**
     * 获取指定舱位类型的最低价格（用于标签显示）
     * 往返航班：去程最低票价 + 回程最低票价
     * 单程航班：单程最低票价
     * 注意：舱位选择器只显示票价，不含机建燃油
     */
    getMinPriceByType(cabinType) {
      console.log('🔍 getMinPriceByType 开始计算:', { cabinType, isRoundTrip: this.isRoundTrip })
      
      // 安全处理展开运算符，确保数组不是null
      const safeDepartureList = Array.isArray(this.departureList) ? this.departureList : []
      const safeReturnList = Array.isArray(this.returnList) ? this.returnList : []
      const safeList = Array.isArray(this.list) ? this.list : []
      
      if (this.isRoundTrip) {
        // 往返航班：分别计算去程和回程的最低票价，然后相加
        console.log('  - 往返航班模式，去程数量:', safeDepartureList.length, '回程数量:', safeReturnList.length)
        
        // 计算去程最低票价（仅票价，不含机建燃油）
        let departureMinPrice = 0
        if (safeDepartureList.length > 0) {
          const departurePrices = safeDepartureList.map(flight => this.getFlightPriceByType(flight, cabinType, true))
            .filter(price => price > 0)
          departureMinPrice = departurePrices.length > 0 ? Math.min(...departurePrices) : 0
        }
        
        // 计算回程最低票价（仅票价，不含机建燃油）
        let returnMinPrice = 0
        if (safeReturnList.length > 0) {
          const returnPrices = safeReturnList.map(flight => this.getFlightPriceByType(flight, cabinType, true))
            .filter(price => price > 0)
          returnMinPrice = returnPrices.length > 0 ? Math.min(...returnPrices) : 0
        }
        
        const totalPrice = departureMinPrice + returnMinPrice
        console.log('  - 往返价格计算结果:', { 
          departureMinPrice, 
          returnMinPrice, 
          totalPrice,
          cabinType 
        })
        
        return totalPrice
      } else {
        // 单程航班：计算单程最低票价（仅票价，不含机建燃油）
        console.log('  - 单程航班模式，航班数量:', safeList.length)
        
        if (!safeList || safeList.length === 0) return 0
        
        const prices = safeList.map(flight => this.getFlightPriceByType(flight, cabinType, true))
          .filter(price => price > 0)
        
        const minPrice = prices.length > 0 ? Math.min(...prices) : 0
        console.log('  - 单程价格计算结果:', { minPrice, cabinType })
        
        return minPrice
      }
    },
    
    /**
     * 跳转到舱位选择页面
     */
    toShipSpace(item) {
      // 添加数据验证
      if (!item) {
        console.error('航班数据为空，无法跳转到舱位选择页面')
        uni.showToast({
          title: '航班数据错误，请重新选择',
          icon: 'none'
        })
        return
      }
      
      if (!this.isRoundTrip) {
        // 单程：直接跳转
        const data = JSON.stringify({
          flightInfo: item,
          searchForm: this.searchForm,
        })
        
        uni.navigateTo({
          url: `/pages/order/ship-space?data=${encodeURIComponent(data)}`
        })
        return
      }
      
      // 往返程：需要两个航班都选择后才能进入下一步
      if (!this.selectedDeparture || !this.selectedReturn) {
        uni.showToast({
          title: '请先选择去程和回程航班',
          icon: 'none'
        })
        return
      }
      
      // 跳转到订单确认页面，传递往返程信息
      this.goToOrderPage()
    },
    
    /**
     * 跳转到舱位选择页面（往返程）
     */
    goToOrderPage() {
      if (!this.selectedDeparture || !this.selectedReturn) {
        uni.showToast({
          title: '请选择完整的往返程航班',
          icon: 'none'
        })
        return
      }
      
      // 往返程：跳转到舱位选择页面，传递往返程信息
      const roundTripData = {
        isRoundTrip: true,
        journeyType: 'RT',
        departure: this.selectedDeparture,
        return: this.selectedReturn,
        searchForm: this.searchForm,
        totalPrice: this.totalPrice
      }
      
      console.log('跳转到往返程舱位选择页面，数据:', roundTripData)
      
      const data = JSON.stringify(roundTripData)
      uni.navigateTo({
        url: `/pages/order/ship-space?data=${encodeURIComponent(data)}`
      })
    },
    
    /**
     * 重新选择航班
     */
    resetSelection(type) {
      if (type === 'departure') {
        this.selectedDeparture = null
      } else if (type === 'return') {
        this.selectedReturn = null
      }
    },
    
    /**
     * 刷新航班数据
     */
    refreshFlights() {
      this.selectedDeparture = null
      this.selectedReturn = null
      this.refresh()
    },
    
    
    /**
     * 获取日历数据
     */
    getCalendarDate() {
      const conDay = new Date()
      const startDay = conDay.getDate()
      const endDay = new Date(conDay.getFullYear(), conDay.getMonth() + 1, 0).getDate()
      let AllDay = endDay - startDay + 1
      for (let i = 0; i < this.monthNum; i++) {
        const monthDay = new Date(conDay.getFullYear(), conDay.getMonth() + 1 + i, 0).getDate()
        AllDay += monthDay
      }
      
      const calendarPriceList = []
      for (let i = 0; i < AllDay; i++) {
        calendarPriceList.push({
          date: this.$getDateStr(i),
        })
      }
      this.calendarPriceList = calendarPriceList
      
      if (this.isRoundTrip && this.currentStep === 'return') {
        const endDateIndex = this.calendarPriceList.findIndex(item => item.date === this.searchForm.endDate)
        this.current = endDateIndex >= 0 ? endDateIndex : 0
      } else {
        const startDateIndex = this.calendarPriceList.findIndex(item => item.date === this.searchForm.startDate)
        this.current = startDateIndex >= 0 ? startDateIndex : 0
      }
      // 安全处理展开运算符
      const safeCalendarPriceList = Array.isArray(this.calendarPriceList) ? this.calendarPriceList : []
      this.departureDateList = [...safeCalendarPriceList];
      this.returnDateList = [...safeCalendarPriceList];
      this.departureCurrent = this.departureDateList.findIndex(item => item.date === this.searchForm.startDate);
      if (this.departureCurrent < 0) this.departureCurrent = 0;
      this.returnCurrent = this.returnDateList.findIndex(item => item.date === this.searchForm.endDate);
      if (this.returnCurrent < 0) this.returnCurrent = 0;
    },
    
    /**
     * 切换日期标签
     */
    changeTabs(index) {
      if (!this.calendarPriceList || !this.calendarPriceList[index]) {
        console.warn('Calendar price list item not found for index:', index)
        return
      }
      this.current = index
      this.searchForm.startDate = this.calendarPriceList[index].date
      this.refresh()
    },
    
    /**
     * 返回上一页
     */
    statusBarBack() {
      uni.navigateBack({
        fail: () => {
          uni.switchTab({
            url: '/pages/index/index'
          })
        }
      })
    },
    
    /**
     * 日历选择回调
     */
    getStartDate({ startStr }) {
      if (this.isRoundTrip && this.currentStep === 'return') {
        this.searchForm.endDate = startStr.dateStr
      } else {
        this.searchForm.startDate = startStr.dateStr
      }
      const dateIndex = this.calendarPriceList.findIndex(item => item.date === startStr.dateStr)
      this.current = dateIndex >= 0 ? dateIndex : 0
      this.refresh()
    },
    changeDepartureTabs(index) {
      if (!this.departureDateList || !this.departureDateList[index]) {
        console.warn('departureDateList item not found for index:', index);
        return;
      }
      this.departureCurrent = index;
      this.searchForm.startDate = this.departureDateList[index].date;
      this.refresh();
    },
    changeReturnTabs(index) {
      if (!this.returnDateList || !this.returnDateList[index]) {
        console.warn('returnDateList item not found for index:', index);
        return;
      }
      this.returnCurrent = index;
      this.searchForm.endDate = this.returnDateList[index].date;
      this.refresh();
    },
    onDepartureCalendarSelect({ startStr }) {
      this.searchForm.startDate = startStr.dateStr;
      this.departureCurrent = this.departureDateList.findIndex(item => item.date === startStr.dateStr);
      this.showDepartureCalendar = false;
      this.refresh();
    },
    onReturnCalendarSelect({ startStr }) {
      this.searchForm.endDate = startStr.dateStr;
      this.returnCurrent = this.returnDateList.findIndex(item => item.date === startStr.dateStr);
      this.showReturnCalendar = false;
      this.refresh();
    },

  }
}
</script>

<template>
  <view class="page-wrap">
    <view class="page-header">
      <custom-navbar :show-icon="true" :height="marginTop" @on-click="statusBarBack">
        <view :style="{ display: 'flex', alignItems: 'center', justifyContent: 'center', marginRight: `${getTopWidth.right}px` }">
          <text style="width: 38%;text-overflow: ellipsis;white-space: nowrap;overflow:hidden;text-align: right;">
            {{ searchForm.startCity }}
          </text>
          <uni-icons custom-prefix="iconfont" type="icon-hangbanqiehuan" size="20" color="#ffffff" style="margin:-2px 18px 0 18px;" />
          <text style="width: 38%;text-overflow: ellipsis;white-space: nowrap;overflow:hidden;text-align: left;">
            {{ searchForm.endCity }}
          </text>
        </view>
      </custom-navbar>
    </view>
    
    <view class="page-content" :style="{ marginTop: `${marginTop}px`, height: `calc(100vh - ${marginTop}px)` }">
      <!-- 单程模式 -->
      <template v-if="!isRoundTrip">
        <!-- 日期选择 -->
        <view class="date-panel">
          <view class="calendar-box">
            <custom-tabs
              v-model="current"
              :list="calendarPriceList"
              :show-price="false"
              @change="changeTabs"
            />
          </view>
          <view class="calendar-tool" @click="showStartDate = true">
            <uni-icons custom-prefix="iconfont" type="icon-rili" size="16" />
            <text>日历</text>
          </view>
        </view>
        
        <!-- 单程航班列表 -->
        <view class="single-flight-list">
          <scroll-view scroll-y class="scroll-box">
            <route-cell 
              v-for="(item, index) in list" 
              v-if="item"
              :key="index" 
              :cell-data="item" 
              @on-click="(flightData) => selectFlight(flightData || item, 'single')" 
            />
            <nomore-panel v-if="!list.length && status === 'nomore'" />
            <u-loadmore v-if="list.length && status === 'nomore'" class="loadmore-box" :status="status" color="#CACACA" font-size="12" nomore-text="没有更多了~" />
          </scroll-view>
        </view>
      </template>
      
              <!-- 往返程模式 -->
      <template v-else>
        <view class="roundtrip-container">
          <view class="date-panel roundtrip-date-panel">
          <view class="date-select-block">
            <view class="calendar-box">
              <custom-tabs
                v-model="departureCurrent"
                :list="departureDateList"
                :show-price="false"
                @change="changeDepartureTabs"
              />
            </view>
            <view class="calendar-tool" @click="showDepartureCalendar = true">
              <uni-icons custom-prefix="iconfont" type="icon-rili" size="16" />
              <text>日历</text>
            </view>
          </view>
          <view class="date-arrow">
            <uni-icons custom-prefix="iconfont" type="icon-hangbanqiehuan" size="18" />
          </view>
          <view class="date-select-block">
            <view class="calendar-box">
              <custom-tabs
                v-model="returnCurrent"
                :list="returnDateList"
                :show-price="false"
                @change="changeReturnTabs"
              />
            </view>
            <view class="calendar-tool" @click="showReturnCalendar = true">
              <uni-icons custom-prefix="iconfont" type="icon-rili" size="16" />
              <text>日历</text>
            </view>
          </view>
        </view>
        <calendar-popup
          v-model="showDepartureCalendar"
          :calendar-price-list="departureDateList"
          :start-date="searchForm.startDate"
          @callback="onDepartureCalendarSelect"
        />
        <calendar-popup
          v-model="showReturnCalendar"
          :calendar-price-list="returnDateList"
          :start-date="searchForm.endDate"
          @callback="onReturnCalendarSelect"
        />
        
        <!-- 已选航班信息栏 -->
        <view v-if="selectedDeparture || selectedReturn" class="selected-flights-bar">
          <view class="selected-info-horizontal">
            <view v-if="selectedDeparture" class="selected-item departure">
              <text class="flight-label">去程:</text>
              <text class="flight-info">{{ selectedDeparture.flightBaseInfoVo && selectedDeparture.flightBaseInfoVo.flightNo }} ¥{{ calculateFlightPrice(selectedDeparture) }}</text>
              <text class="reset-btn" @click="resetSelection('departure')">重选</text>
            </view>
            <view v-if="selectedReturn" class="selected-item return">
              <text class="flight-label">回程:</text>
              <text class="flight-info">{{ selectedReturn.flightBaseInfoVo && selectedReturn.flightBaseInfoVo.flightNo }} ¥{{ calculateFlightPrice(selectedReturn) }}</text>
              <text class="reset-btn" @click="resetSelection('return')">重选</text>
            </view>
          </view>
        </view>
        

        <!-- 往返程航班列表（左右并排） -->
        <view class="roundtrip-flights" :class="{ 'has-selected-bar': selectedDeparture || selectedReturn, 'has-bottom-buttons': canConfirmOrder }">
          <!-- 左侧：去程航班 -->
          <view class="flight-column departure-column">
            <view class="column-header">
              <text class="column-title">去程航班</text>
              <text class="column-subtitle">{{ searchForm.startCity }} → {{ searchForm.endCity }}</text>
              <text class="column-date">{{ searchForm.startDate }}</text>

            </view>
            

            
            <view class="flight-list">

              
              <scroll-view scroll-y class="scroll-container">
                <view 
                  v-for="(item, index) in departureList" 
                  v-if="item"
                  :key="index"
                  class="flight-item"
                  @click="selectFlight(item, 'departure')"
                >
                  <route-cell-mini 
                    :cell-data="item" 
                    flight-type="departure" 
                    :is-selected="selectedDeparture && selectedDeparture.flightBaseInfoVo && selectedDeparture.flightBaseInfoVo.flightNo === item.flightBaseInfoVo.flightNo"
                  />
                </view>
                
                <!-- 去程加载状态 -->
                <view v-if="departureStatus === 'loading'" class="loading-container">
                  <u-loading-icon text="正在加载去程航班..." />
                </view>
                <nomore-panel v-else-if="!departureList.length && departureStatus === 'nomore'" message="暂无去程航班" />
                <u-loadmore v-else-if="departureList.length && departureStatus === 'nomore'" :status="departureStatus" color="#CACACA" font-size="12" nomore-text="没有更多了~" />
              </scroll-view>
            </view>
          </view>
          
          <!-- 中间分隔线 -->
          <view class="column-divider"></view>
          
          <!-- 右侧：回程航班 -->
          <view class="flight-column return-column">
            <view class="column-header">
              <text class="column-title">回程航班</text>
              <text class="column-subtitle">{{ searchForm.endCity }} → {{ searchForm.startCity }}</text>
              <text class="column-date">{{ searchForm.endDate }}</text>

            </view>
            <view class="flight-list">
              <scroll-view scroll-y class="scroll-container">
                <view 
                  v-for="(item, index) in returnList" 
                  v-if="item"
                  :key="index"
                  class="flight-item"
                  @click="selectFlight(item, 'return')"
                >
                  <route-cell-mini 
                    :cell-data="item" 
                    flight-type="return" 
                    :is-selected="selectedReturn && selectedReturn.flightBaseInfoVo && selectedReturn.flightBaseInfoVo.flightNo === item.flightBaseInfoVo.flightNo"
                  />
                </view>
                
                <!-- 回程加载状态 -->
                <view v-if="returnStatus === 'loading'" class="loading-container">
                  <u-loading-icon text="正在加载回程航班..." />
                </view>
                <nomore-panel v-else-if="!returnList.length && returnStatus === 'nomore'" message="暂无回程航班" />
                <u-loadmore v-else-if="returnList.length && returnStatus === 'nomore'" :status="returnStatus" color="#CACACA" font-size="12" nomore-text="没有更多了~" />
              </scroll-view>
            </view>
          </view>
        </view>
        </view>
      </template>
      
      <!-- 底部按钮区域 -->
      <view v-if="canConfirmOrder" class="bottom-buttons">
        <view class="button-container">
          <u-button 
            class="next-btn-full" 
            type="primary" 
            size="normal"
            @click="goToOrderPage"
          >
            下一步 ¥{{ totalPrice }}
          </u-button>
        </view>
      </view>
    </view>
    
    <!-- 日历弹窗 -->
    <calendar-popup
      v-model="showStartDate"
      :price-list="calendarPriceList"
      :start-date="searchForm.startDate"
      :mode="3"
      @on-change="getStartDate"
    />
    

  </view>
</template>

<style lang="scss">
page{
  width: 100%;
  height: 100%;
  background-color: #ffffff !important;
  overflow-x: hidden;
  overflow-y: auto;
}
</style>

<style lang="scss" scoped>
.page-wrap {
  width: 100%;
  min-height: 100vh;
  background: #ffffff !important;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.page-content {
  width: 100%;
  overflow: hidden;
  position: relative;
  background: #ffffff !important;
  display: flex;
  flex-direction: column;
}

/* 往返程容器样式 */
.roundtrip-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  background: #ffffff !important;
}

/* 往返程布局样式 */
.roundtrip-flights {
  width: 100%;
  display: flex;
  background: #ffffff !important;
  position: relative;
  overflow: hidden;
  flex: 1;
  min-height: 0;
  
  .flight-column {
    flex: 1;
    width: 50%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #ffffff !important;
    position: relative;
    
    .column-header {
      padding: 12px 16px;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      
      .column-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }
      
      .column-subtitle {
        font-size: 14px;
        color: #666;
        margin-bottom: 2px;
      }
      
      .column-date {
        font-size: 12px;
        color: #999;
      }
    }
    
    .flight-list {
      flex: 1;
      min-height: 0;
      overflow: hidden;
      position: relative;
      background: #ffffff !important;
      
      .scroll-container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 8px 8px 88px 8px;
        background: #ffffff !important;
      }
      
      .flight-item {
        margin-bottom: 8px;
        transition: all 0.2s ease;
        border-radius: 8px;
        overflow: hidden;
      }
      
      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;
        flex-shrink: 0;
        background: #ffffff !important;
      }
    }
  }
  
  /* 中间分隔线 */
  .column-divider {
    width: 1px;
    height: 100%;
    background: #e9ecef;
    flex-shrink: 0;
  }
}

/* 单程日期选择面板 */
.date-panel {
  width: 100%;
  padding: 12px 16px;
  background: #ffffff !important;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  
  .calendar-box {
    flex: 1 1 0%;
    min-width: 0;
    margin-right: 12px;
    overflow: hidden;
    ::v-deep .tabBlock > scroll-view {
      width: 100% !important;
    }
  }
  
  .calendar-tool {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 6px 8px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    margin-left: 8px;
    transition: all 0.2s ease;
    &:hover {
      background: #e9ecef;
    }
    uni-icons {
      margin-bottom: 2px;
    }
    text {
      font-size: 10px;
      color: #333;
      margin-left: 0;
      margin-top: 0;
    }
  }
}

/* 往返程日期选择面板 */
.roundtrip-date-panel {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #ffffff !important;
  border-bottom: 1px solid #e9ecef;
  .date-select-block {
    flex: 1 1 0%;
    min-width: 0;
    display: flex;
    align-items: center;
    .calendar-box {
      flex: 1 1 0%;
      min-width: 0;
      margin-right: 8px;
      overflow: hidden;
      ::v-deep .tabBlock > scroll-view {
        width: 100% !important;
      }
    }
    .calendar-tool {
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 6px 8px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      cursor: pointer;
      margin-left: 8px;
      transition: all 0.2s ease;
      &:hover {
        background: #e9ecef;
      }
      uni-icons {
        margin-bottom: 2px;
      }
      text {
        font-size: 10px;
        color: #333;
        margin-left: 0;
        margin-top: 0;
      }
    }
  }
  .date-arrow {
    flex-shrink: 0;
    margin: 0 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* 已选航班信息栏 */
.selected-flights-bar {
  width: 100%;
  padding: 16px;
  background: #ffffff !important;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  
  .selected-info-horizontal {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    gap: 16px;
    
    .selected-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 12px;
      background: #f8f9fa;
      border-radius: 4px;
      
      .flight-label {
        font-size: 12px;
        color: #666;
        margin-right: 8px;
      }
      
      .flight-info {
        font-size: 14px;
        color: #333;
        font-weight: 500;
        margin-right: 12px;
      }
      
      .reset-btn {
        font-size: 12px;
        color: #dc3545;
        padding: 4px 8px;
        border: 1px solid #dc3545;
        border-radius: 4px;
        cursor: pointer;
      }
    }
  }
}

/* 底部按钮区域 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  background: #ffffff !important;
  border-top: 1px solid #e9ecef;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
  z-index: 1000;
  
  .button-container {
    display: flex;
    align-items: center;
    
    .next-btn-full {
      width: 100%;
      height: 40px;
      border-radius: 20px;
      font-size: 16px;
      font-weight: 600;
      
      ::v-deep .u-button__text {
        font-weight: 600;
      }
    }
  }
}



/* ======================== 通用组件样式 ======================== */
.loadmore-box {
  padding: 20px;
  text-align: center;
  background: #ffffff !important;
}

/* ======================== 响应式适配 ======================== */
@media (max-width: 600px) {
  .roundtrip-flights {
    .flight-column {
      .column-header {
        padding: 10px 12px;
        
        .column-title {
          font-size: 13px;
        }
        
        .column-subtitle {
          font-size: 11px;
        }
        
        .column-date {
          font-size: 10px;
        }
      }
      
      .flight-list {
        .scroll-container {
          padding: 6px;
        }
        
        .flight-item {
          margin-bottom: 6px;
        }
      }
    }
  }
  
  .selected-flights-bar {
    padding: 10px 12px;
    
    .selected-info-horizontal {
      gap: 12px;
      
      .selected-item {
        padding: 6px 10px;
        
        .flight-label {
          font-size: 11px;
        }
        
        .flight-info {
          font-size: 13px;
        }
        
        .reset-btn {
          font-size: 11px;
          padding: 3px 6px;
        }
      }
    }
  }
  
  .date-panel.roundtrip-dates {
    padding: 12px;
    
    .date-item {
      .date-label {
        font-size: 11px;
      }
      
      .date-value {
        font-size: 13px;
      }
    }
    
    .refresh-btn {
      padding: 6px 10px;
      
      text {
        font-size: 9px;
      }
    }
  }
}

@media (max-width: 480px) {
  .roundtrip-flights {
    .flight-column {
      .column-header {
        padding: 8px 10px;
        
        .column-subtitle {
          font-size: 10px;
        }
      }
    }
  }
  
  .bottom-buttons {
    padding: 12px 16px;
    
    .button-container {
      .next-btn-full {
        height: 36px;
        
        ::v-deep .u-button__text {
          font-size: 15px;
        }
      }
    }
  }
}

/* ======================== 强制禁用深色模式影响 ======================== */
@media (prefers-color-scheme: dark) {
  .page-wrap,
  .page-content,
  .roundtrip-container,
  .roundtrip-flights,
  .flight-column,
  .flight-list,
  .scroll-container,
  .loading-container,
  .date-panel,
  .selected-flights-bar,
  .bottom-buttons,
  .loadmore-box {
    background: #ffffff !important;
    color: #333333 !important;
  }
  
  .date-panel {
    background: #ffffff !important;
    border-bottom-color: #e9ecef !important;
    
    .calendar-tool {
      background: #f8f9fa !important;
      border-color: #e9ecef !important;
      
      text {
        color: #333333 !important;
      }
      
      &:hover {
        background: #e9ecef !important;
      }
    }
  }
  
  .selected-flights-bar {
    background: #ffffff !important;
    border-bottom-color: #e9ecef !important;
    
    .selected-info-horizontal {
      .selected-item {
        background: #f8f9fa !important;
        
        .flight-label {
          color: #666 !important;
        }
        
        .flight-info {
          color: #333333 !important;
        }
      }
    }
  }
  
  .column-divider {
    background: #e9ecef !important;
  }
  
  .bottom-buttons {
    background: #ffffff !important;
    border-top-color: #e9ecef !important;
  }
}


</style>
