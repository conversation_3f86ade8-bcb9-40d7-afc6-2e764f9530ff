<script>
export default {
  data() {
    return {
      status: 'loadmore',
      loading: false,
      list: [],
      searchForm: {
        startDate: '',
        startCityCode: '',
        endCityCode: '',
        flightNo: '',
      },
      info: {
        airCode: '',
        arrAirport: '',
        arrAirportName: '',
        arrCityName: '',
        arrTerminal: '',
        arrTime: '',
        depTerminal: '',
        deptAirport: '',
        deptAirportName: '',
        deptCityName: '',
        deptDate: '',
        deptTime: '',
        flightNo: '',
        planeType: '',
        stop: '',
      },
      height: 0,
      flightData: null, // 新增：存储从route.vue传递过来的航班数据
      // 往返程相关数据
      isRoundTrip: false,
      roundTripData: null, // 往返程数据
      selectedDepartureCabin: null, // 选中的去程舱位
      selectedReturnCabin: null, // 选中的回程舱位
      departureList: [], // 去程舱位列表
      returnList: [], // 回程舱位列表
      departureInfo: {}, // 去程航班信息
      returnInfo: {}, // 回程航班信息
      selectedCabinType: 'economy', // 选择的舱位类型：economy-经济舱, business-头等/公务舱
      isNavigating: false, // 防重复跳转标志位
    }
  },
  onLoad({ data }) {
    // 初始化时重置跳转标志位
    this.isNavigating = false
    
    try {
      // 先进行URL解码，然后解析JSON
      const paramData = JSON.parse(decodeURIComponent(data))
      
      // 判断是否为往返程
      if (paramData.isRoundTrip) {
        this.isRoundTrip = true
        this.roundTripData = paramData
        this.searchForm = paramData.searchForm || {}
        this.parseRoundTripData()
      } else if (paramData.flightInfo) {
        // 单程：从 route.vue 传递过来的新数据结构
        this.isRoundTrip = false
        this.searchForm = paramData.searchForm || {}
        this.flightData = paramData.flightInfo
        this.parseFlightData()
      } else {
        // 兼容旧的数据结构
        this.isRoundTrip = false
        this.searchForm = paramData
        this.queryList()
      }
    } catch (error) {
      // 如果解析失败，使用默认值或返回上一页
      uni.showToast({
        title: '参数错误',
        icon: 'error'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  },
  // 页面卸载时清理状态
  beforeUnmount() {
    this.isNavigating = false
  },
  computed: {
    getTopWidth() {
      const menuButton = uni.getMenuButtonBoundingClientRect()
      const screen = uni.getWindowInfo()
      const right = (screen.screenWidth - menuButton.right) + menuButton.width + 11 - 50
      const width = screen.screenWidth - ((screen.screenWidth - menuButton.right) + menuButton.width) - 50 - 11
      return {
        width,
        right,
      }
    },
    // 去程舱位列表（根据选择的舱位类型过滤）
    filteredDepartureList() {
      // 确保 this.departureList 是有效数组
      if (!Array.isArray(this.departureList) || this.departureList.length === 0) {
        return []
      }
      
      const result = this.filterCabinsByType(this.departureList)
      
      // 双重安全检查：确保返回的数组不包含无效元素
      const safeResult = Array.isArray(result) ? result.filter(item => 
        item && 
        typeof item === 'object' && 
        (item.cabinCls || item.cabinCode || item.cabin)
      ) : []
      
      return safeResult
    },
    // 回程舱位列表（根据选择的舱位类型过滤）
    filteredReturnList() {
      // 确保 this.returnList 是有效数组
      if (!Array.isArray(this.returnList) || this.returnList.length === 0) {
        return []
      }
      
      const result = this.filterCabinsByType(this.returnList)
      
      // 双重安全检查：确保返回的数组不包含无效元素
      const safeResult = Array.isArray(result) ? result.filter(item => 
        item && 
        typeof item === 'object' && 
        (item.cabinCls || item.cabinCode || item.cabin)
      ) : []
      
      return safeResult
    },
    // 单程舱位列表（根据选择的舱位类型过滤）
    filteredList() {
      // 确保 this.list 是有效数组
      if (!Array.isArray(this.list) || this.list.length === 0) {
        return []
      }
      
      const result = this.filterCabinsByType(this.list)
      
      // 双重安全检查：确保返回的数组不包含无效元素
      const safeResult = Array.isArray(result) ? result.filter(item => 
        item && 
        typeof item === 'object' && 
        (item.cabinCls || item.cabinCode || item.cabin)
      ) : []
      
      console.log('🔍 filteredList 计算调试:', {
        originalLength: this.list.length,
        filteredLength: result ? result.length : 0,
        safeLength: safeResult.length,
        selectedCabinType: this.selectedCabinType
      })
      
      return safeResult
    },
    // 合并往返程舱位列表（智能匹配去程和回程）
    combinedCabinList() {
      // 确保基础数据都是数组
      const safeDepartureList = Array.isArray(this.filteredDepartureList) ? this.filteredDepartureList : []
      const safeReturnList = Array.isArray(this.filteredReturnList) ? this.filteredReturnList : []
      
      console.log('🔍 combinedCabinList 开始处理:', {
        isRoundTrip: this.isRoundTrip,
        departureCount: safeDepartureList.length,
        returnCount: safeReturnList.length,
        selectedCabinType: this.selectedCabinType
      })
      
      if (!this.isRoundTrip || safeDepartureList.length === 0) {
        console.log('🔍 combinedCabinList: 不是往返程或去程列表为空，返回空数组')
        return []
      }
      
      const combinedList = []
      
      // 遍历去程舱位（以去程为主）
      safeDepartureList.forEach((departureCabin, index) => {
        // 增强数据验证：确保departureCabin是有效对象
        if (!departureCabin || typeof departureCabin !== 'object') {
          console.warn('🚨 combinedCabinList: 发现无效去程舱位:', departureCabin)
          return
        }
        
        // 获取去程舱位代码，增加安全检查
        const departureCabinCode = departureCabin.cabinCls || departureCabin.cabinCode || departureCabin.cabin
        if (!departureCabinCode) {
          console.warn('🚨 combinedCabinList: 去程舱位缺少舱位代码:', departureCabin)
          return
        }
        
        // 尝试找到回程相同舱位代码的舱位（修正字段名）
        let returnCabin = safeReturnList.find(cabin => {
          if (!cabin || typeof cabin !== 'object') {
            return false
          }
          const returnCabinCode = cabin.cabinCls || cabin.cabinCode || cabin.cabin
          return returnCabinCode && returnCabinCode === departureCabinCode
        })
        
        // 如果没找到相同舱位代码，选择回程第一个可用舱位
        if (!returnCabin && safeReturnList.length > 0) {
          returnCabin = safeReturnList.find(cabin => 
            cabin && typeof cabin === 'object' && (cabin.cabinCls || cabin.cabinCode || cabin.cabin)
          )
        }
        
        // 如果过滤后的回程舱位列表为空，从原始回程列表中选择第一个
        if (!returnCabin && Array.isArray(this.returnList) && this.returnList.length > 0) {
          returnCabin = this.returnList.find(cabin => 
            cabin && typeof cabin === 'object' && (cabin.cabinCls || cabin.cabinCode || cabin.cabin)
          )
        }
        
        // 只有当去程和回程舱位都存在且有效时才添加到列表
        if (!returnCabin || typeof returnCabin !== 'object') {
          console.warn('🚨 combinedCabinList: 无法找到有效的回程舱位，跳过组合:', { departureCabin, returnCabin })
          return
        }
        
        // 计算价格，增加错误处理（仅计算票价，不含机建燃油）
        let departurePrice = 0
        let returnPrice = 0
        
        try {
          departurePrice = this.calculateCabinPrice(departureCabin, true)
          returnPrice = this.calculateCabinPrice(returnCabin, true)
        } catch (error) {
          console.error('🚨 combinedCabinList: 计算舱位价格时出错:', error, { departureCabin, returnCabin })
          return
        }
        
        // 确保价格为有效数值
        if (isNaN(departurePrice) || isNaN(returnPrice) || departurePrice < 0 || returnPrice < 0) {
          console.warn('🚨 combinedCabinList: 舱位价格无效，跳过组合:', { departurePrice, returnPrice })
          return
        }
        
        // 安全构建舱位信息对象 - 修复cabinClsName字母代码问题
        const safeDepartureCabin = {
          ...departureCabin,
          cabinName: this.getCabinDisplayInfo(departureCabin), // 直接使用推断的中文名称
          cabinClsName: this.getCabinDisplayInfo(departureCabin), // 修复cabinClsName为中文名称
          cabinCode: departureCabin.cabinCls || departureCabin.cabinCode || departureCabin.cabin || 'Y',
          discount: departureCabin.discount || this.getCabinDisplayInfo(departureCabin)
        }
        
        const safeReturnCabin = {
          ...returnCabin,
          cabinName: this.getCabinDisplayInfo(returnCabin), // 直接使用推断的中文名称
          cabinClsName: this.getCabinDisplayInfo(returnCabin), // 修复cabinClsName为中文名称
          cabinCode: returnCabin.cabinCls || returnCabin.cabinCode || returnCabin.cabin || 'Y',
          discount: returnCabin.discount || this.getCabinDisplayInfo(returnCabin)
        }
        
        // 构建组合项
        const combinedItem = {
          id: `${departureCabin.cabinPriceId || departureCabin.cabinCls || departureCabin.cabinCode || index}_${returnCabin.cabinPriceId || returnCabin.cabinCls || returnCabin.cabinCode || index}`,
          departureCabin: safeDepartureCabin,
          returnCabin: safeReturnCabin,
          totalPrice: Math.round(departurePrice + returnPrice), // 确保总价为整数
          hasInsurance: true, // 默认显示保险选项
          departurePrice: Math.round(departurePrice),
          returnPrice: Math.round(returnPrice)
        }
        
        // 最终验证：确保组合项完整且有效
        if (combinedItem.departureCabin && 
            combinedItem.returnCabin && 
            combinedItem.totalPrice > 0 && 
            combinedItem.departureCabin.cabinCode && 
            combinedItem.returnCabin.cabinCode) {
          combinedList.push(combinedItem)
        } else {
          console.warn('🚨 combinedCabinList: 组合舱位数据不完整，跳过:', combinedItem)
        }
      })
      
      // 按总价格排序
      const sortedList = combinedList.sort((a, b) => a.totalPrice - b.totalPrice)
      
      // 最终过滤，确保所有数据都有效
      const validList = sortedList.filter(item => {
        if (!item || typeof item !== 'object') {
          console.warn('🚨 combinedCabinList: 最终检查发现无效项(非对象):', item)
          return false
        }
        if (!item.departureCabin || typeof item.departureCabin !== 'object' || 
            !item.returnCabin || typeof item.returnCabin !== 'object') {
          console.warn('🚨 combinedCabinList: 最终检查发现舱位信息无效:', item)
          return false
        }
        if (!item.totalPrice || item.totalPrice <= 0 || isNaN(item.totalPrice)) {
          console.warn('🚨 combinedCabinList: 最终检查发现价格无效:', item)
          return false
        }
        if (!item.departureCabin.cabinCode || !item.returnCabin.cabinCode) {
          console.warn('🚨 combinedCabinList: 最终检查发现舱位代码缺失:', item)
          return false
        }
        return true
      })
       
      console.log('🔍 combinedCabinList 处理完成:', {
        原始组合数: combinedList.length,
        排序后数量: sortedList.length,
        最终有效数量: validList.length,
        最终结果: validList
      })
      
      // 确保总是返回数组
      return Array.isArray(validList) ? validList : []
    },
  },
  created() {
    const windowInfo = uni.getWindowInfo()
    const menu = wx.getMenuButtonBoundingClientRect()
    this.height = (menu.top - windowInfo.statusBarHeight) * 2 + menu.height + windowInfo.statusBarHeight
  },
  methods: {
    // 新增：解析从route.vue传递过来的航班数据
    parseFlightData() {
      if (!this.flightData) {
        uni.showToast({
          title: '航班数据错误',
          icon: 'error'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
        return
      }

      this.loading = true
      this.status = 'loading'
      uni.showLoading({
        title: '正在查询舱位...',
        mask: false,
      })

      try {
        // 获取舱位列表，修正字段名，并过滤无效项
        const rawCabins = this.flightData.cabinClses || this.flightData.cabinList || this.flightData.cabins || []
        
        console.log('🔍 parseFlightData 原始数据:', {
          rawCabinsLength: rawCabins.length,
          rawCabins: rawCabins,
          flightData: this.flightData
        })
        
        // 严格的数据过滤和验证，并修复cabinClsName字段
        this.list = Array.isArray(rawCabins) ? rawCabins.filter(cabin => {
          // 严格验证：确保cabin是有效对象且包含必要字段
          if (!cabin || typeof cabin !== 'object') {
            console.warn('🚨 parseFlightData: 发现无效cabin (null/undefined/非对象):', cabin)
            return false
          }
          
          // 验证舱位代码
          const cabinCode = cabin.cabinCls || cabin.cabinCode || cabin.cabin
          if (!cabinCode || (typeof cabinCode !== 'string' && typeof cabinCode !== 'number')) {
            console.warn('🚨 parseFlightData: 舱位缺少有效代码:', cabin)
            return false
          }
          
          // 验证价格信息（至少要有一个价格字段）
          const hasPrice = cabin.priceAdult || cabin.adultOrigPrice || cabin.price
          if (!hasPrice) {
            console.warn('🚨 parseFlightData: 舱位缺少价格信息:', cabin)
            return false
          }
          
          return true
        }).map(cabin => {
          // 修复cabinClsName字段，将字母代码转换为中文名称
          return {
            ...cabin,
            cabinClsName: this.getCabinDisplayInfo(cabin)
          }
        }) : []
        
        console.log('🔍 parseFlightData 过滤后数据:', {
          filteredLength: this.list.length,
          filteredList: this.list
        })
        
        // 再次安全检查：确保没有undefined或null元素
        const validCount = this.list.filter(item => item && typeof item === 'object').length
        if (validCount !== this.list.length) {
          console.error('🚨 parseFlightData: 检测到无效元素，重新过滤')
          this.list = this.list.filter(item => item && typeof item === 'object')
        }
        
        // 获取航班基本信息，兼容不同的数据结构
        const flightInfo = this.flightData.flightBaseInfoVo || this.flightData
        
        this.info = {
          ...flightInfo,
          meal: this.list.length > 0 ? (this.list[0].meal || 'N') : 'N',
        }
        
        this.status = 'nomore'
        
        console.log('✅ parseFlightData 完成:', {
          最终舱位数量: this.list.length,
          航班信息: this.info
        })
        
      } catch (error) {
        console.error('🚨 parseFlightData解析失败:', error)
        this.list = []
        this.info = {}
        this.status = 'nomore'
        
        uni.showToast({
          title: '数据解析失败',
          icon: 'error'
        })
      } finally {
        uni.hideLoading()
        this.loading = false
      }
    },

    // 保留原有的查询逻辑作为兼容
    queryList() {
      if (this.status === 'loading' || this.status === 'nomore')
        return

      this.list = []
      this.loading = true
      this.status = 'loading'
      uni.showLoading({
        title: '正在加载...',
        mask: false,
      })
      
      try {
        const list = uni.getStorageSync('flightList')

        if (list && list.length) {
          const flightObj = list.find(item => item.flightBaseInfoVo.flightNo === this.searchForm.flightNo)
          if (flightObj) {
            // 过滤无效舱位数据
            const rawCabins = flightObj.cabinClses || []
            
            console.log('🔍 queryList 原始数据:', {
              rawCabinsLength: rawCabins.length,
              flightNo: this.searchForm.flightNo,
              flightObj: flightObj
            })
            
            // 严格过滤舱位数据，并修复cabinClsName字段
            this.list = Array.isArray(rawCabins) ? rawCabins.filter(cabin => {
              if (!cabin || typeof cabin !== 'object') {
                console.warn('🚨 queryList: 发现无效cabin:', cabin)
                return false
              }
              
              const cabinCode = cabin.cabinCls || cabin.cabinCode || cabin.cabin
              if (!cabinCode) {
                console.warn('🚨 queryList: 舱位缺少代码:', cabin)
                return false
              }
              
              const hasPrice = cabin.priceAdult || cabin.adultOrigPrice || cabin.price
              if (!hasPrice) {
                console.warn('🚨 queryList: 舱位缺少价格信息:', cabin)
                return false
              }
              
              return true
            }).map(cabin => {
              // 修复cabinClsName字段，将字母代码转换为中文名称
              return {
                ...cabin,
                cabinClsName: this.getCabinDisplayInfo(cabin)
              }
            }) : []
            
            console.log('🔍 queryList 过滤后数据:', this.list.length, '个有效舱位')
            
            this.info = {
              ...flightObj.flightBaseInfoVo,
              meal: this.list.length > 0 ? (this.list[0].meal || 'N') : 'N',
            }
          } else {
            console.warn('🚨 queryList: 未找到匹配的航班:', this.searchForm.flightNo)
          }
        } else {
          console.warn('🚨 queryList: 本地存储中没有航班数据')
        }
      } catch (error) {
        console.error('🚨 queryList 处理失败:', error)
        this.list = []
        this.info = {}
        
        uni.showToast({
          title: '加载航班数据失败',
          icon: 'error'
        })
      } finally {
        this.status = 'nomore'
        uni.hideLoading()
        this.loading = false
        
        console.log('✅ queryList 完成:', {
          最终舱位数量: this.list.length,
          航班信息: this.info
        })
      }
    },
    
    // 新增：解析往返程数据
    parseRoundTripData() {
      if (!this.roundTripData || !this.roundTripData.departure || !this.roundTripData.return) {
        uni.showToast({
          title: '往返程数据错误',
          icon: 'error'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
        return
      }

      this.loading = true
      this.status = 'loading'
      uni.showLoading({
        title: '正在处理...',
        mask: false,
      })

      try {
        // 解析去程数据 - 安全处理展开运算符
        const departureData = this.roundTripData.departure || {}
        const rawDepartureCabins = departureData.cabinClses || departureData.cabinList || departureData.cabins || []
        
        console.log('🔍 parseRoundTripData 去程原始数据:', {
          rawDepartureCabinsLength: rawDepartureCabins.length,
          departureData: departureData
        })
        
        // 严格过滤去程舱位数据，并修复cabinClsName字段
        this.departureList = Array.isArray(rawDepartureCabins) ? rawDepartureCabins.filter(cabin => {
          if (!cabin || typeof cabin !== 'object') {
            console.warn('🚨 parseRoundTripData: 去程发现无效cabin:', cabin)
            return false
          }
          
          const cabinCode = cabin.cabinCls || cabin.cabinCode || cabin.cabin
          if (!cabinCode) {
            console.warn('🚨 parseRoundTripData: 去程舱位缺少代码:', cabin)
            return false
          }
          
          const hasPrice = cabin.priceAdult || cabin.adultOrigPrice || cabin.price
          if (!hasPrice) {
            console.warn('🚨 parseRoundTripData: 去程舱位缺少价格:', cabin)
            return false
          }
          
          return true
        }).map(cabin => {
          // 修复cabinClsName字段，将字母代码转换为中文名称
          return {
            ...cabin,
            cabinClsName: this.getCabinDisplayInfo(cabin)
          }
        }) : []
        
        console.log('🔍 parseRoundTripData 去程过滤后:', this.departureList.length, '个有效舱位')
        
        const departureFlightInfo = departureData.flightBaseInfoVo || departureData || {}
        // 确保展开的对象不是null
        const safeDepartureInfo = (departureFlightInfo && typeof departureFlightInfo === 'object') ? departureFlightInfo : {}
        this.departureInfo = {
          ...safeDepartureInfo,
          meal: this.departureList.length > 0 ? (this.departureList[0].meal || 'N') : 'N',
        }
        
        // 解析回程数据 - 安全处理展开运算符
        const returnData = this.roundTripData.return || {}
        const rawReturnCabins = returnData.cabinClses || returnData.cabinList || returnData.cabins || []
        
        console.log('🔍 parseRoundTripData 回程原始数据:', {
          rawReturnCabinsLength: rawReturnCabins.length,
          returnData: returnData
        })
        
        // 严格过滤回程舱位数据，并修复cabinClsName字段
        this.returnList = Array.isArray(rawReturnCabins) ? rawReturnCabins.filter(cabin => {
          if (!cabin || typeof cabin !== 'object') {
            console.warn('🚨 parseRoundTripData: 回程发现无效cabin:', cabin)
            return false
          }
          
          const cabinCode = cabin.cabinCls || cabin.cabinCode || cabin.cabin
          if (!cabinCode) {
            console.warn('🚨 parseRoundTripData: 回程舱位缺少代码:', cabin)
            return false
          }
          
          const hasPrice = cabin.priceAdult || cabin.adultOrigPrice || cabin.price
          if (!hasPrice) {
            console.warn('🚨 parseRoundTripData: 回程舱位缺少价格:', cabin)
            return false
          }
          
          return true
        }).map(cabin => {
          // 修复cabinClsName字段，将字母代码转换为中文名称
          return {
            ...cabin,
            cabinClsName: this.getCabinDisplayInfo(cabin)
          }
        }) : []
        
        console.log('🔍 parseRoundTripData 回程过滤后:', this.returnList.length, '个有效舱位')
        
        const returnFlightInfo = returnData.flightBaseInfoVo || returnData || {}
        // 确保展开的对象不是null
        const safeReturnInfo = (returnFlightInfo && typeof returnFlightInfo === 'object') ? returnFlightInfo : {}
        this.returnInfo = {
          ...safeReturnInfo,
          meal: this.returnList.length > 0 ? (this.returnList[0].meal || 'N') : 'N',
        }

        this.status = 'nomore'
        
        console.log('✅ parseRoundTripData 完成:', {
          去程舱位数量: this.departureList.length,
          回程舱位数量: this.returnList.length,
          去程信息: this.departureInfo,
          回程信息: this.returnInfo
        })
        
      } catch (error) {
        console.error('🚨 parseRoundTripData解析失败:', error)
        this.departureList = []
        this.returnList = []
        this.departureInfo = {}
        this.returnInfo = {}
        this.status = 'nomore'
        
        uni.showToast({
          title: '往返程数据解析失败',
          icon: 'error'
        })
      } finally {
        uni.hideLoading()
        this.loading = false
      }
    },
    

        
    // 切换舱位类型
    switchCabinType(type) {
      this.selectedCabinType = type
      
      // 重置已选择的舱位
      this.selectedDepartureCabin = null
      this.selectedReturnCabin = null
    },
    
    // 根据舱位类型过滤舱位列表
    filterCabinsByType(cabinList) {
      // 基础验证：确保输入是有效数组
      if (!Array.isArray(cabinList) || cabinList.length === 0) {
        console.log('🔍 filterCabinsByType: 输入不是有效数组或为空')
        return []
      }
      
      console.log('🔍 filterCabinsByType 开始处理:', {
        inputLength: cabinList.length,
        selectedCabinType: this.selectedCabinType,
        firstCabin: cabinList[0]
      })
      
      // 先进行基础数据验证，过滤掉无效元素
      const validCabins = cabinList.filter(cabin => {
        // 严格的数据验证
        if (!cabin || typeof cabin !== 'object') {
          console.warn('🚨 filterCabinsByType发现无效cabin (null/undefined/非对象):', cabin)
          return false
        }
        
        // 检查是否有舱位代码
        const cabinCode = cabin.cabinCls || cabin.cabinCode || cabin.cabin
        if (!cabinCode || typeof cabinCode !== 'string') {
          console.warn('🚨 filterCabinsByType发现无效舱位代码:', cabin)
          return false
        }
        
        return true
      })
      
      console.log('🔍 基础验证后有效舱位数量:', validCabins.length)
      
      // 如果没有有效舱位，直接返回空数组
      if (validCabins.length === 0) {
        console.warn('⚠️ 没有有效的舱位数据')
        return []
      }
      
      // 根据舱位类型进行过滤
      const filteredCabins = validCabins.filter(cabin => {
        try {
          // 安全获取舱位代码并转换为大写
          const cabinCode = (cabin.cabinCls || cabin.cabinCode || cabin.cabin || '').toString().toUpperCase()
          
          // 如果舱位代码为空，跳过该项
          if (!cabinCode) {
            console.warn('🚨 舱位代码为空，跳过:', cabin)
            return false
          }
          
          // 根据选择的舱位类型进行过滤
          if (this.selectedCabinType === 'economy') {
            // 经济舱：包含经济舱和超级经济舱
            const isEconomy = ['Y', 'M', 'H', 'K', 'L', 'Q', 'T', 'E', 'U', 'V', 'W', 'S', 'N', 'R', 'G', 'X', 'B'].includes(cabinCode)
            if (!isEconomy) {
              console.log(`🔍 舱位${cabinCode}不是经济舱，已过滤`)
            }
            return isEconomy
          } else if (this.selectedCabinType === 'business') {
            // 头等/公务舱
            const isBusiness = ['F', 'C', 'J', 'D', 'I', 'Z', 'P', 'A', 'O'].includes(cabinCode)
            if (!isBusiness) {
              console.log(`🔍 舱位${cabinCode}不是商务/头等舱，已过滤`)
            }
            return isBusiness
          }
          
          // 默认情况下保留所有舱位
          return true
        } catch (error) {
          console.error('🚨 filterCabinsByType处理cabin时出错:', error, cabin)
          return false
        }
      })
      
      console.log('🔍 filterCabinsByType 完成:', {
        原始数量: cabinList.length,
        有效数量: validCabins.length,
        过滤后数量: filteredCabins.length,
        舱位类型: this.selectedCabinType
      })
      
      // 最终安全检查：确保返回的每个元素都是有效的
      const finalResult = filteredCabins.filter(cabin => 
        cabin && 
        typeof cabin === 'object' && 
        (cabin.cabinCls || cabin.cabinCode || cabin.cabin)
      )
      
      if (finalResult.length !== filteredCabins.length) {
        console.warn('🚨 最终安全检查发现并移除了无效元素')
      }
      
      return finalResult
    },
    
    // 获取舱位类型的最低价格（用于标签显示）
    // 往返程：去程最低票价 + 回程最低票价
    // 单程：单程最低票价
    // 注意：舱位选择器只显示票价，不含机建燃油
    getMinPriceByType(cabinType) {
      console.log('🔍 ship-space.vue getMinPriceByType 开始计算:', { 
        cabinType, 
        isRoundTrip: this.isRoundTrip 
      })
      
      // 安全处理展开运算符，确保所有数组都不是null
      const safeDepartureList = Array.isArray(this.departureList) ? this.departureList : []
      const safeReturnList = Array.isArray(this.returnList) ? this.returnList : []
      const safeList = Array.isArray(this.list) ? this.list : []
      
      if (this.isRoundTrip) {
        // 往返程：分别计算去程和回程的最低票价，然后相加
        console.log('  - 往返程模式，去程舱位数量:', safeDepartureList.length, '回程舱位数量:', safeReturnList.length)
        
        // 计算去程最低票价
        let departureMinPrice = 0
        if (safeDepartureList.length > 0) {
          const departureFilteredCabins = safeDepartureList.filter(cabin => this.filterCabinByType(cabin, cabinType))
          const departurePrices = departureFilteredCabins.map(cabin => this.calculateCabinPrice(cabin, true))
            .filter(price => price > 0)
          departureMinPrice = departurePrices.length > 0 ? Math.min(...departurePrices) : 0
        }
        
        // 计算回程最低票价
        let returnMinPrice = 0
        if (safeReturnList.length > 0) {
          const returnFilteredCabins = safeReturnList.filter(cabin => this.filterCabinByType(cabin, cabinType))
          const returnPrices = returnFilteredCabins.map(cabin => this.calculateCabinPrice(cabin, true))
            .filter(price => price > 0)
          returnMinPrice = returnPrices.length > 0 ? Math.min(...returnPrices) : 0
        }
        
        const totalPrice = departureMinPrice + returnMinPrice
        console.log('  - 往返程价格计算结果:', { 
          departureMinPrice, 
          returnMinPrice, 
          totalPrice,
          cabinType 
        })
        
        return totalPrice
      } else {
        // 单程：计算单程最低票价
        console.log('  - 单程模式，舱位数量:', safeList.length)
        
        if (!safeList || safeList.length === 0) return 0
        
        const filteredCabins = safeList.filter(cabin => this.filterCabinByType(cabin, cabinType))
        
        if (filteredCabins.length === 0) return 0
        
        const prices = filteredCabins.map(cabin => this.calculateCabinPrice(cabin, true))
          .filter(price => price > 0)
        
        const minPrice = prices.length > 0 ? Math.min(...prices) : 0
        console.log('  - 单程价格计算结果:', { minPrice, cabinType })
        
        return minPrice
      }
    },
    
    // 按舱位类型过滤舱位的辅助方法
    filterCabinByType(cabin, cabinType) {
      // 增强数据验证：确保cabin是有效对象
      if (!cabin || typeof cabin !== 'object') {
        return false
      }
      
      try {
        // 修正字段名：使用 cabinCls 而不是 cabinCode，增加安全检查
        const cabinCode = (cabin.cabinCls || cabin.cabinCode || cabin.cabin || '').toUpperCase()
        
        // 如果没有舱位代码，跳过该项
        if (!cabinCode) {
          return false
        }
        
        if (cabinType === 'economy') {
          // 经济舱：包含经济舱和超级经济舱
          return ['Y', 'M', 'H', 'K', 'L', 'Q', 'T', 'E', 'U', 'V', 'W', 'S', 'N', 'R', 'G', 'X', 'B'].includes(cabinCode)
        } else if (cabinType === 'business') {
          // 头等/公务舱
          return ['F', 'C', 'J', 'D', 'I', 'Z', 'P', 'A', 'O'].includes(cabinCode)
        }
        
        return true
      } catch (error) {
        console.error('filterCabinByType处理cabin时出错:', error, cabin)
        return false
      }
    },
      
    // 计算舱位价格
    // @param {Object} cabin 舱位数据
    // @param {Boolean} onlyTicketPrice 是否仅计算票价含税（不含机建燃油费用）
    calculateCabinPrice(cabin, onlyTicketPrice = false) {
      // 增强数据验证
      if (!cabin || typeof cabin !== 'object') {
        console.warn('calculateCabinPrice收到无效cabin参数:', cabin)
        return 0
      }
      
      try {
        // 优先使用 priceAdult，如果为空则使用 adultOrigPrice
        const adultPrice = parseFloat(cabin.priceAdult || cabin.adultOrigPrice || cabin.adultPrice || 0)
        const taxFee = parseFloat(cabin.taxFeeAdult || cabin.adultTax || cabin.taxFee || 0)
        
        if (onlyTicketPrice) {
          // 返回票价含税，不含机建燃油费用
          const ticketPriceWithTax = adultPrice
          console.log('calculateCabinPrice - 票价含税模式:', { 
            adultPrice, 
            taxFee, 
            ticketPriceWithTax,
            cabin: cabin.cabinCls || cabin.cabinCode 
          })
          return isNaN(ticketPriceWithTax) || ticketPriceWithTax < 0 ? 0 : Math.round(ticketPriceWithTax)
        }
        
        // 计算包含所有费用的总价
        const oilFee = parseFloat(cabin.oilFeeAdult || cabin.oilFee || 0)
        const airportFee = parseFloat(cabin.amtAdultAirPortFee || cabin.airportFee || 0)
        
        // 验证所有价格组件都是有效数字
        if (isNaN(adultPrice) || isNaN(taxFee) || isNaN(oilFee) || isNaN(airportFee)) {
          console.warn('舱位价格包含无效数值:', { adultPrice, taxFee, oilFee, airportFee, cabin })
          return 0
        }
        
        const totalPrice = adultPrice  + oilFee + airportFee
        
        // 确保总价是有效数字且不是负数
        if (isNaN(totalPrice) || totalPrice < 0) {
          console.warn('计算出的总价格无效:', totalPrice, cabin)
          return 0
        }
        
        console.log('calculateCabinPrice - 全价模式:', { 
          adultPrice, 
          taxFee, 
          oilFee, 
          airportFee, 
          totalPrice,
          cabin: cabin.cabinCls || cabin.cabinCode 
        })
        
        return Math.round(totalPrice) // 返回整数价格
      } catch (error) {
        console.error('计算舱位价格时发生错误:', error, cabin)
        return 0
      }
    },

    toCreate(item, flightType) {
      console.log('🔍 toCreate被调用 - 详细参数验证:', { 
        item, 
        flightType, 
        isRoundTrip: this.isRoundTrip,
        itemType: typeof item,
        hasItem: !!item
      })
      
      // 第一层验证：基础参数检查
      if (!item || typeof item !== 'object') {
        console.error('🚨 toCreate收到无效item参数 (null/undefined/非对象):', item)
        uni.showToast({
          title: '舱位数据错误，请重新选择',
          icon: 'error'
        })
        return
      }
      
      // 第二层验证：检查舱位代码
      const cabinCode = item.cabinCls || item.cabinCode || item.cabin
      if (!cabinCode) {
        console.error('🚨 toCreate: 舱位缺少代码信息:', item)
        uni.showToast({
          title: '舱位代码缺失，请重新选择',
          icon: 'error'
        })
        return
      }
      
      // 第三层验证：检查价格信息
      const hasPrice = item.priceAdult || item.adultOrigPrice || item.price
      if (!hasPrice) {
        console.error('🚨 toCreate: 舱位缺少价格信息:', item)
        uni.showToast({
          title: '舱位价格信息缺失，请重新选择',
          icon: 'error'
        })
        return
      }
      
      // 第四层验证：检查舱位基本信息
      const cabinName = this.getCabinDisplayInfo(item)
      if (!cabinName) {
        console.warn('⚠️ toCreate: 舱位缺少名称信息，使用默认值:', item)
      }
      
      console.log('✅ toCreate 数据验证通过，开始处理业务逻辑')
      
      if (!this.isRoundTrip) {
        // 单程：直接跳转到订单创建页面
        
        // 防重复跳转检查
        if (this.isNavigating) {
          console.log('⚠️ 单程模式：正在跳转中，忽略重复调用')
          return
        }
        
        // 设置跳转标志位
        this.isNavigating = true
        
        try {
          const deptDate = this.info.deptDate || this.$formatDateStr(this.info.deptTime, 'yyyy-mm-dd') || ''
          const valvePrice = (this.info && this.info.valvePrice) || 0
          // 安全处理展开运算符
          const safeInfo = (this.info && typeof this.info === 'object') ? this.info : {}
          const safeItem = (item && typeof item === 'object') ? item : {}
          
          const orderData = { ...safeInfo, ...safeItem, deptDate, valvePrice, peopleType: this.searchForm.peopleType }
          console.log('设置单程订单数据:', orderData)
          
          uni.setStorageSync('create_flight', orderData)
          uni.navigateTo({
            url: '/pages/order/create',
            success: () => {
              console.log('✅ 单程模式：成功跳转到订单创建页面')
            },
            fail: (error) => {
              console.error('❌ 单程模式：跳转失败:', error)
              // 跳转失败时重置标志位，允许重试
              this.isNavigating = false
              uni.showToast({
                title: '页面跳转失败，请重试',
                icon: 'error'
              })
            }
          })
        } catch (error) {
          console.error('处理单程订单数据时出错:', error)
          // 发生错误时重置标志位
          this.isNavigating = false
          uni.showToast({
            title: '数据处理错误',
            icon: 'error'
          })
        }
        return
      }
      
      // 往返程：记录选择的舱位
      if (flightType === 'departure') {
        this.selectedDepartureCabin = item
        console.log('已选择去程舱位:', this.getCabinDisplayInfo(item))
        
        // 不自动选择回程舱位，让用户手动选择
        if (this.selectedReturnCabin) {
          // 如果回程舱位已经选择，才跳转到订单创建页面
          this.goToCreateOrder()
        } else {
          // 提示用户选择回程舱位
          uni.showToast({
            title: '请选择回程舱位',
            icon: 'none',
            duration: 2000
          })
        }
      } else if (flightType === 'return') {
        this.selectedReturnCabin = item
        console.log('已选择回程舱位:', this.getCabinDisplayInfo(item))

        if (this.selectedDepartureCabin) {
          // 两个舱位都已选择，跳转到订单创建页面
          this.goToCreateOrder()
        } else {
          // 提示选择去程舱位
          uni.showToast({
            title: '请选择去程舱位',
            icon: 'none',
            duration: 2000
          })
        }
      }
    },
    
    // 往返程：跳转到订单创建页面
    goToCreateOrder() {
      // 防重复跳转检查
      if (this.isNavigating) {
        console.log('⚠️ 正在跳转中，忽略重复调用')
        return
      }
      
      if (!this.selectedDepartureCabin || !this.selectedReturnCabin) {
        uni.showToast({
          title: '请选择完整的往返程舱位',
          icon: 'none'
        })
        return
      }
      
      // 设置跳转标志位
      this.isNavigating = true
      
      // 确保舱位数据完整性 - 安全处理展开运算符
      const departureCabin = (this.selectedDepartureCabin && typeof this.selectedDepartureCabin === 'object') ? this.selectedDepartureCabin : {}
      const returnCabin = (this.selectedReturnCabin && typeof this.selectedReturnCabin === 'object') ? this.selectedReturnCabin : {}
      
      // 合并舱位信息和航班信息，确保 order-panel 需要的字段在顶级
      const departureFlightInfo = (this.departureInfo && typeof this.departureInfo === 'object') ? this.departureInfo : {}
      const safeDeparture = {
        ...departureCabin,
        ...departureFlightInfo, // 将航班信息提升到顶级
        // 确保基本字段存在
        cabinName: this.getCabinDisplayInfo(this.selectedDepartureCabin),
        cabinCode: this.selectedDepartureCabin?.cabinCode || this.selectedDepartureCabin?.cabinCls || 'Y',
        priceAdult: this.selectedDepartureCabin?.priceAdult || this.selectedDepartureCabin?.adultOrigPrice || 0,
        // 保留嵌套结构以防其他地方使用
        flightBaseInfoVo: departureFlightInfo,
        passengers: [], // 初始化乘客列表
        meal: this.selectedDepartureCabin?.meal || 'N'
      }
      
      // 合并舱位信息和航班信息，确保 order-panel 需要的字段在顶级
      const returnFlightInfo = (this.returnInfo && typeof this.returnInfo === 'object') ? this.returnInfo : {}
      const safeReturn = {
        ...returnCabin,
        ...returnFlightInfo, // 将航班信息提升到顶级
        // 确保基本字段存在
        cabinName: this.getCabinDisplayInfo(this.selectedReturnCabin),
        cabinCode: this.selectedReturnCabin?.cabinCode || this.selectedReturnCabin?.cabinCls || 'Y',
        priceAdult: this.selectedReturnCabin?.priceAdult || this.selectedReturnCabin?.adultOrigPrice || 0,
        // 保留嵌套结构以防其他地方使用
        flightBaseInfoVo: returnFlightInfo,
        passengers: [], // 初始化乘客列表
        meal: this.selectedReturnCabin?.meal || 'N'
      }
      
      // 构建往返程订单数据，匹配create.vue期望的数据结构
      const orderData = {
        isRoundTrip: true,
        journeyType: 'RT',
        // create.vue直接访问这些字段
        departure: safeDeparture,
        return: safeReturn,
        // 保留原有结构以防其他地方使用
        flightInfo: {
          departure: safeDeparture,
          return: safeReturn,
          departureInfo: this.departureInfo || {},
          returnInfo: this.returnInfo || {}
        },
        searchForm: this.searchForm || {},
        totalPrice: this.roundTripData?.totalPrice || 0,
        passengers: [] // 初始化乘客列表
      }
      
      console.log('🚀 发送到create页面的完整数据:')
      console.log('   - isRoundTrip:', orderData.isRoundTrip)
      console.log('   - departure.flightNo:', orderData.departure.flightNo)
      console.log('   - departure.airName:', orderData.departure.airName)
      console.log('   - return.flightNo:', orderData.return.flightNo)
      console.log('   - return.airName:', orderData.return.airName)
      console.log('   - departure完整对象:', orderData.departure)
      console.log('   - return完整对象:', orderData.return)
      
      const data = JSON.stringify(orderData)
      
      // 执行跳转，并添加错误处理
      uni.navigateTo({
        url: `/pages/order/create?data=${encodeURIComponent(data)}`,
        success: () => {
          console.log('✅ 成功跳转到订单创建页面')
        },
        fail: (error) => {
          console.error('❌ 跳转失败:', error)
          // 跳转失败时重置标志位，允许重试
          this.isNavigating = false
          uni.showToast({
            title: '页面跳转失败，请重试',
            icon: 'error'
          })
        }
      })
         },
     
     // 处理单程舱位卡片点击
     handleSingleCabinClick(item) {
       console.log('处理单程舱位卡片点击:', item)
       this.toCreate(item)
     },
     
     // 处理单程预订按钮点击
     handleSingleBookClick(item) {
       console.log('处理单程预订按钮点击:', item)
       this.toCreate(item)
     },
    
         // 处理舱位卡片点击
     handleCabinClick(index) {
       console.log('处理舱位卡片点击，index:', index, 'combinedCabinList长度:', this.combinedCabinList.length)
       
       // 增强参数验证
       if (typeof index !== 'number' || index < 0 || !Array.isArray(this.combinedCabinList)) {
         console.error('无效的index或combinedCabinList:', { index, combinedCabinList: this.combinedCabinList })
         uni.showToast({
           title: '选择数据错误',
           icon: 'error'
         })
         return
       }
       
       if (index >= this.combinedCabinList.length) {
         console.error('index超出数组范围:', { index, length: this.combinedCabinList.length })
         uni.showToast({
           title: '选择项不存在',
           icon: 'error'
         })
         return
       }
       
       const item = this.combinedCabinList[index]
       console.log('选中的舱位组合项:', item)
       
       // 验证item的有效性
       if (!item || typeof item !== 'object') {
         console.error('选中的舱位组合项无效:', item)
         uni.showToast({
           title: '舱位数据无效',
           icon: 'error'
         })
         return
       }
       
       this.selectCombinedCabin(item)
     },
     
     // 处理预订按钮点击
     handleBookClick(index) {
       console.log('处理预订按钮点击，index:', index, 'combinedCabinList长度:', this.combinedCabinList.length)
       
       // 增强参数验证
       if (typeof index !== 'number' || index < 0 || !Array.isArray(this.combinedCabinList)) {
         console.error('无效的index或combinedCabinList:', { index, combinedCabinList: this.combinedCabinList })
         uni.showToast({
           title: '预订数据错误',
           icon: 'error'
         })
         return
       }
       
       if (index >= this.combinedCabinList.length) {
         console.error('index超出数组范围:', { index, length: this.combinedCabinList.length })
         uni.showToast({
           title: '预订项不存在',
           icon: 'error'
         })
         return
       }
       
       const item = this.combinedCabinList[index]
       console.log('预订的舱位组合项:', item)
       
       // 验证item的有效性
       if (!item || typeof item !== 'object') {
         console.error('预订的舱位组合项无效:', item)
         uni.showToast({
           title: '预订数据无效',
           icon: 'error'
         })
         return
       }
       
       this.selectCombinedCabin(item)
     },
       
     // 选择合并舱位（往返程一起选择）
     selectCombinedCabin(combinedItem) {
       console.log('开始选择合并舱位:', combinedItem)
       
       // 增强数据验证
       if (!combinedItem || typeof combinedItem !== 'object') {
         console.error('combinedItem无效:', combinedItem)
         uni.showToast({
           title: '舱位数据错误',
           icon: 'error'
         })
         return
       }
       
       // 验证去程舱位
       if (!combinedItem.departureCabin || typeof combinedItem.departureCabin !== 'object') {
         console.error('去程舱位数据无效:', combinedItem.departureCabin)
         uni.showToast({
           title: '去程舱位数据不完整',
           icon: 'error'
         })
         return
       }
       
       // 验证回程舱位
       if (!combinedItem.returnCabin || typeof combinedItem.returnCabin !== 'object') {
         console.error('回程舱位数据无效:', combinedItem.returnCabin)
         uni.showToast({
           title: '回程舱位数据不完整',
           icon: 'error'
         })
         return
       }
       
       // 验证必要字段
       const departureCabinCode = combinedItem.departureCabin.cabinCode || combinedItem.departureCabin.cabinCls || combinedItem.departureCabin.cabinCode
       const returnCabinCode = combinedItem.returnCabin.cabinCode || combinedItem.returnCabin.cabinCls || combinedItem.returnCabin.cabinCode
       
       if (!departureCabinCode || !returnCabinCode) {
         console.error('舱位代码缺失:', { departureCabinCode, returnCabinCode })
         uni.showToast({
           title: '舱位代码数据不完整',
           icon: 'error'
         })
         return
       }
       
       // 验证价格
       if (!combinedItem.totalPrice || isNaN(combinedItem.totalPrice) || combinedItem.totalPrice <= 0) {
         console.error('总价格无效:', combinedItem.totalPrice)
         uni.showToast({
           title: '价格数据错误',
           icon: 'error'
         })
         return
       }
       
       // 设置去程和回程舱位
       this.selectedDepartureCabin = combinedItem.departureCabin
       this.selectedReturnCabin = combinedItem.returnCabin
       
       console.log('成功设置选中舱位:', {
         departure: this.selectedDepartureCabin,
         return: this.selectedReturnCabin
       })
       
       // 显示选择提示（短暂显示）
       uni.showToast({
         title: `已选择往返舱位，总价¥${combinedItem.totalPrice}`,
         icon: 'success',
         duration: 1000
       })
       
       // 立即跳转，避免延迟导致的重复调用问题
       this.goToCreateOrder()
     },
    
             // 获取舱位显示信息（舱位名称）
    getCabinDisplayInfo(cabin) {
      // 增强数据验证
      if (!cabin || typeof cabin !== 'object') {
        console.warn('getCabinDisplayInfo收到无效cabin参数:', cabin)
        return '经济舱'
      }
      
      // 获取舱位代码
      const cabinCode = cabin.cabinCode || cabin.cabinCls || cabin.cabin || ''
      
      // 直接根据舱位代码推断舱位类型，不再信任cabinClsName字段
      // 因为接口返回的cabinClsName可能就是字母代码（如"A"、"U"等）
      if (cabinCode && typeof cabinCode === 'string') {
        const code = cabinCode.toUpperCase()
        
        // 头等舱代码
        if (['F', 'A'].includes(code)) {
          return '头等舱'
        }
        // 公务舱代码
        if (['C', 'J', 'D', 'I', 'Z', 'P', 'O'].includes(code)) {
          return '公务舱'
        }
        // 超级经济舱代码（部分航司使用）
        if (['W', 'S'].includes(code)) {
          return '超级经济舱'
        }
        // 经济舱代码
        if (['Y', 'M', 'H', 'K', 'L', 'Q', 'T', 'E', 'U', 'V', 'N', 'R', 'G', 'X', 'B'].includes(code)) {
          return '经济舱'
        }
      }
      
      // 最后检查cabinName字段，但只接受纯中文名称
      const cabinName = cabin.cabinName || cabin.cabinClsName || ''
      if (cabinName && cabinName !== '') {
        // 只接受纯中文舱位名称，排除单字母或包含字母的名称
        if (!(/^[A-Z]$/.test(cabinName)) && !(/[A-Z]/.test(cabinName)) && 
            (cabinName === '经济舱' || cabinName === '公务舱' || cabinName === '头等舱' || cabinName === '超级经济舱')) {
          return cabinName
        }
      }
      
      // 默认返回经济舱
      return '经济舱'
    },
    
    // 根据舱位代码判断舱位类型
    getCabinTypeByCode(cabinCode) {
      const code = cabinCode.toUpperCase()
      if (['F', 'C', 'J', 'D', 'I', 'Z', 'P', 'A', 'O'].includes(code)) {
        return 'business'
      }
      return 'economy'
    },
    
    // 折扣显示函数已删除，等待接口提供实际折扣数据
    },
  }
</script>

<template>
  <view class="page-wrap">
    <view class="page-header">
      <custom-navbar :show-icon="true" :height="statusBarHeight" need-two-line @on-click="statusBarBack">
        <view :style="{ display: 'flex', alignItems: 'center', justifyContent: 'center', marginRight: `${getTopWidth.right}px` }">
          <text style="width: 38%;text-overflow: ellipsis;white-space: nowrap;overflow:hidden;text-align: right;">
            {{ searchForm.startCity }}
          </text>
          <uni-icons custom-prefix="iconfont" type="icon-hangbanqiehuan" size="20" color="#ffffff" style="margin:-2px 18px 0 18px;" />
          <text style="width: 38%;text-overflow: ellipsis;white-space: nowrap;overflow:hidden;text-align: left;">
            {{ searchForm.endCity }}
          </text>
        </view>
      </custom-navbar>
    </view>
    <view class="page-content" :style="{ top: `${height - 20}px` }">
      <!-- 单程航班基本信息面板 -->
      <view v-if="!isRoundTrip" class="panel-box">
        <u-skeleton
          rows="3"
          :loading="loading"
          title-width="90%"
          title-height="24"
          :rows-height="['22', '52', '22']"
          :rows-width="['90%', '90%', '90%']"
        >
          <view class="panel-title">
            <text class="title-text">{{ info.deptTime ? $formatDateStr(info.deptTime, 'yyyy年mm月dd日') : '' }}</text>
            <text class="title-text">{{ info.deptTime ? $formatDateStr(info.deptTime, 'WW') : '' }}</text>
          </view>
          <view class="step-item">
            <view class="step-item-left">
              <view class="time">
                {{ info.deptTime ? $formatDateStr(info.deptTime, 'hh:MM') : '' }}
              </view>
              <view v-if="info.deptTime || info.arrTime" class="line">
                <view class="triangle" />
              </view>
              <view class="time">
                {{ info.arrTime ? $formatDateStr(info.arrTime, 'hh:MM') : '' }}
              </view>
            </view>
            <view class="step-item-right">
              <text class="title">
                {{ info.deptAirportName }}{{ info.depTerminal }}
              </text>
              <text v-if="info.deptAirportName || info.arrAirportName">
                -
              </text>
              <text class="title">
                {{ info.arrAirportName }}{{ info.arrTerminal }}
              </text>
            </view>
          </view>
          <view class="info-air">
            <u-image :src="info.airIconUrl" :show-loading="true" width="16px" height="16px" />
            <text class="air-text">{{ info.airName || '' }} {{ info.flightNo || '' }}</text>
            <view v-if="!info.share" class="info-panel">
              <text v-if="info.planeType">
                ｜
              </text>
              <text>{{ info.planeType }}</text>
              <text v-if="info.meal === 'Y'">
                ｜
              </text>
              <text v-if="info.meal === 'Y'">
                有餐食
              </text>
              <!-- <text>|</text>
                      <text>准点率100</text> -->
            </view>
            <view v-if="info.share" class="real-air">
              <i class="iconfont icon-down" style="font-size: 32rpx;color: #999999;margin-left: 8rpx;" />
              <text class="real-air-text">
                实际承运
              </text>
              <u-image :src="info.operateIconUrl" :show-loading="true" width="16px" height="16px" />
              <text class="air-text">{{ info.operateAirName }} {{ info.operateFlightNo }}</text>
            </view>
          </view>
          <view v-if="info.share" class="info-box">
            <view class="info-panel">
              <text>{{ info.planeType }}</text>
              <text v-if="info.meal === 'Y'">
                ｜
              </text>
              <text v-if="info.meal === 'Y'">
                有餐食
              </text>
              <!-- <text>|</text>
                      <text>准点率100</text> -->
            </view>
          </view>
        </u-skeleton>
      </view>
      
      <!-- 往返程航班信息展示 -->
      <view v-if="isRoundTrip" class="round-trip-info">
        <!-- 去程航班信息 -->
        <view class="flight-info-section departure-section">
          <view class="section-header">
            <text class="section-title">去程航班</text>
            <text class="route-info">{{ searchForm.startCity }} → {{ searchForm.endCity }}</text>
            <view v-if="selectedDepartureCabin" class="selected-cabin">
              <text class="selected-text">已选：{{ getCabinDisplayInfo(selectedDepartureCabin) }}</text>
              <uni-icons custom-prefix="iconfont" type="icon-xuanzhong" size="14" color="#52c41a" />
            </view>
          </view>
          <view class="flight-details">
            <view class="flight-time">
              <text class="time">{{ departureInfo.deptTime ? $formatDateStr(departureInfo.deptTime, 'hh:MM') : '' }}</text>
              <text class="airport">{{ departureInfo.deptAirportName }}</text>
            </view>
            <view class="flight-duration">
              <text class="flight-no">{{ departureInfo.flightNo }}</text>
            </view>
            <view class="flight-time">
              <text class="time">{{ departureInfo.arrTime ? $formatDateStr(departureInfo.arrTime, 'hh:MM') : '' }}</text>
              <text class="airport">{{ departureInfo.arrAirportName }}</text>
            </view>
          </view>
        </view>
        
        <!-- 回程航班信息 -->
        <view class="flight-info-section return-section">
          <view class="section-header">
            <text class="section-title">回程航班</text>
            <text class="route-info">{{ searchForm.endCity }} → {{ searchForm.startCity }}</text>
            <view v-if="selectedReturnCabin" class="selected-cabin">
              <text class="selected-text">已选：{{ getCabinDisplayInfo(selectedReturnCabin) }}</text>
              <uni-icons custom-prefix="iconfont" type="icon-xuanzhong" size="14" color="#52c41a" />
            </view>
          </view>
          <view class="flight-details">
            <view class="flight-time">
              <text class="time">{{ returnInfo.deptTime ? $formatDateStr(returnInfo.deptTime, 'hh:MM') : '' }}</text>
              <text class="airport">{{ returnInfo.deptAirportName }}</text>
            </view>
            <view class="flight-duration">
              <text class="flight-no">{{ returnInfo.flightNo }}</text>
            </view>
            <view class="flight-time">
              <text class="time">{{ returnInfo.arrTime ? $formatDateStr(returnInfo.arrTime, 'hh:MM') : '' }}</text>
              <text class="airport">{{ returnInfo.arrAirportName }}</text>
            </view>
          </view>
        </view>
        
        <!-- 往返程进度提示 -->
        <view v-if="(selectedDepartureCabin || selectedReturnCabin) && !(selectedDepartureCabin && selectedReturnCabin)" class="progress-tip">
          <text class="tip-text" v-if="!selectedDepartureCabin">请选择去程舱位</text>
          <text class="tip-text" v-else-if="!selectedReturnCabin">请选择回程舱位</text>
        </view>
      </view>
      
      <!-- 舱位类型选择 -->
      <view class="cabin-type-selector">
        <view class="selector-header">
          <text class="selector-title">舱位等级</text>
        </view>
        <view class="selector-tabs">
          <view 
            class="selector-tab" 
            :class="{ 'active': selectedCabinType === 'economy' }" 
            @click="switchCabinType('economy')"
          >
            <text class="tab-label">经济舱</text>
          </view>
          <view 
            class="selector-tab" 
            :class="{ 'active': selectedCabinType === 'business' }" 
            @click="switchCabinType('business')"
          >
            <text class="tab-label">公务/头等舱</text>
            <text class="tab-tag">超值</text>
          </view>
        </view>
      </view>

      
      <view class="cell-box" :class="{ 'round-trip-mode': isRoundTrip }">
        <!-- 单程舱位列表 -->
        <template v-if="!isRoundTrip">
          <view v-if="filteredList.length > 0" class="cabin-list">
            <view 
              v-for="(item, index) in filteredList" 
              v-if="item && (item.cabinCls || item.cabinCode || item.cabin)"
              :key="index" 
              class="cabin-item single-cabin-horizontal" 
              @click="handleSingleCabinClick(item)"
            >
              <!-- 横向布局：价格信息在顶部 -->
              <view class="cabin-header-horizontal">
                <view class="price-main">
                  <text class="currency">¥</text>
                  <text class="amount">{{ item ? calculateCabinPrice(item, true) : 0 }}</text>
                  <text class="note">含税费</text>
                </view>
                <view class="book-btn">
                  <u-button type="primary" size="small" @click.stop="handleSingleBookClick(item)">
                    订
                  </u-button>
                </view>
              </view>
              
              <!-- 舱位信息行 -->
              <view class="cabin-info-horizontal">
                <view class="cabin-tag-horizontal">
                  <text class="cabin-name">{{ getCabinDisplayInfo(item) }}</text>

                </view>
                <view class="cabin-services">
                  <text v-if="item.meal && item.meal === 'Y'" class="service-item">含餐食</text>
                </view>
              </view>
              
              <!-- 退改服务信息行已删除 -->
            </view>
          </view>
          <view v-else class="empty-state">
            <text class="empty-text">暂无可选舱位</text>
          </view>
        </template>
        
        <!-- 往返程舱位列表 -->
        <template v-else>
          <view v-if="combinedCabinList.length > 0" class="cabin-list">
            <view 
              v-for="(item, index) in combinedCabinList" 
              v-if="item && item.departureCabin && item.returnCabin && item.totalPrice"
              :key="index" 
              class="cabin-item combined-cabin-horizontal" 
              @click="handleCabinClick(index)"
            >
              <!-- 横向布局：价格信息在顶部 -->
              <view class="cabin-header-horizontal">
                <view class="price-main">
                  <text class="currency">¥</text>
                  <text class="amount">{{ item.totalPrice && !isNaN(item.totalPrice) ? Math.round(item.totalPrice) : 0 }}</text>
                  <text class="note">含税费</text>
                </view>
                <view class="book-btn">
                  <u-button type="primary" size="small" @click.stop="handleBookClick(index)">
                    订
                  </u-button>
                </view>
              </view>
              
              <!-- 横向布局：去程和返程信息 -->
              <view class="trip-info-horizontal">
                <view class="trip-item">
                  <text class="trip-label">去</text>
                  <text class="cabin-class">{{ item.departureCabin ? getCabinDisplayInfo(item.departureCabin) : '经济舱' }}</text>
                  <text v-if="item.departureCabin && item.departureCabin.meal === 'Y'" class="meal-tag">含餐食</text>
                </view>
              </view>
              
              <view class="trip-info-horizontal">
                <view class="trip-item">
                  <text class="trip-label">返</text>
                  <text class="cabin-class">{{ item.returnCabin ? getCabinDisplayInfo(item.returnCabin) : '经济舱' }}</text>
                  <text v-if="item.returnCabin && item.returnCabin.meal === 'Y'" class="meal-tag">含餐食</text>
                </view>
              </view>
              
              <!-- 保障服务信息已删除 -->
            </view>
          </view>
          <view v-else class="empty-state">
            <text class="empty-text">暂无可选舱位</text>
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
page{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  
  /* 确保移动端滚动体验 */
  -webkit-overflow-scrolling: touch;
}
</style>

<style lang="scss" scoped>
.line{
  position: relative;
  width: 48rpx;
  height: 11rpx;
  border-bottom: 1px solid #D8D8D8;
  margin: 0 16rpx;
  .triangle{
    position: absolute;
    right: -1px;
    bottom: -1px;
    height: 0px;
    width: 0px;
    border-style: solid;
    border-width:  6px 0 0 6px;
    border-color: transparent transparent transparent #D8D8D8;
  }
}
.page-header{
  .blank-box{
    height: 25px;
  }
}

.page-content{
  position: absolute;
  width: 100%;
  height: 32px;
  padding: 24px 8px;

  ::v-deep .panel-box{
    padding: 24rpx 32rpx 24rpx 32rpx;
    margin-top: 6px;
    background: #FFFFFF;
    box-shadow: 0px 2px 6px 0px rgba(202,202,202,0.42);
    border-radius: 8px;

    .step-row{
      position: relative;
    }

    .step-item{
      display: flex;
      align-items: flex-end;
      margin: 16rpx 0 16rpx 0;

      &-left {
        width: 280rpx;
        display: flex;
        align-items: center;
        margin-right: 16rpx;
      }
      &-right{
        font-size: 24rpx;
        margin-bottom: 9rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 34rpx;
      }

      .time{
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100rpx;
        height: 56rpx;
        font-size: 40rpx;
        font-weight: bold;
      }
    }

    .info-air{
      display: flex;
      align-items: center;
      justify-content: flex-start;
      color: #666666;
      line-height: 36rpx;
      font-size: 24rpx;
      .air-text {
        margin-left: 8rpx;
      }
      .real-air{
        &-text{
          margin-right: 8rpx;
        }
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
    }
    .info-box{
      margin-top: 8rpx;
      font-size: 24rpx;
      margin-left: 40rpx;
      line-height: 32rpx;
      color: #999;
    }
    .info-panel{
      color: #999;
    }
    .panel-title{
      width: 100%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      .title-text {
        margin-right: 8px;
        font-size: 14px;
        color: #333333;
        line-height: 24px;
      }
    }

    .u-skeleton__wrapper__content__title{
      margin: 12px auto 0 !important;
    }
    .u-skeleton__wrapper__content__rows{
      margin: 12px auto 0 !important;
    }
  }

     .cell-box{
     margin-top: 1px;
     overflow-x: hidden;
     overflow-y: auto;
     overflow-scrolling: touch;
     padding-bottom: calc(8px + env(safe-area-inset-bottom));
     
     // 往返程模式下的样式调整
     &.round-trip-mode {
       // 设置明确的高度限制，确保可以滚动
       height: calc(100vh - 240px - 6px); // 适配往返程信息区域的额外高度
       max-height: calc(100vh - 240px - 6px);
       margin-top: 0;
       padding: 16px;
       background: #f8f9fa;
       // 确保滚动设置不被覆盖
       overflow-y: auto !important;
       overflow-x: hidden !important;
       -webkit-overflow-scrolling: touch !important;
     }
     
     // 单程模式保持原有高度
     &:not(.round-trip-mode) {
       height: calc(100vh - 183px - 6px);
       padding: 8px;
     }
    
    .cabin-list {
      padding: 0;
      
      .cabin-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #FFFFFF;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 8px;
        box-shadow: 0px 2px 6px 0px rgba(202,202,202,0.42);
        transition: all 0.2s ease;
        
        &:active {
          transform: scale(0.98);
          background: #f8f9fa;
        }
        
        .cabin-info {
          flex: 1;
          margin-right: 12px;
          
          .cabin-header {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            
            .cabin-name {
              font-size: 16px;
              font-weight: 600;
              color: #333;
              margin-right: 8px;
            }
            
            .cabin-code {
              background: #e9ecef;
              color: #666;
              font-size: 12px;
              padding: 2px 6px;
              border-radius: 4px;
              margin-right: 8px;
            }
            
            .discount {
              background: #fff3cd;
              color: #856404;
              font-size: 12px;
              padding: 2px 6px;
              border-radius: 4px;
            }
          }
          
          .cabin-details {
            display: flex;
            align-items: center;
            
            .seat-info {
              font-size: 12px;
              color: #666;
              margin-right: 12px;
            }
            
            .meal-info {
              font-size: 12px;
              color: #28a745;
              background: #d4edda;
              padding: 2px 6px;
              border-radius: 4px;
            }
          }
        }
        
        .price-info {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          margin-right: 12px;
          
          .price-main {
            display: flex;
            align-items: baseline;
            
            .currency {
              font-size: 14px;
              color: #f5222d;
              margin-right: 2px;
            }
            
            .amount {
              font-size: 20px;
              font-weight: 600;
              color: #f5222d;
            }
          }
          
          .price-desc {
            font-size: 12px;
            color: #999;
            margin-top: 2px;
          }
        }
        
        .book-btn {
          flex-shrink: 0;
        }
        
        // 新增：单程舱位横向布局样式
        &.single-cabin-horizontal {
          flex-direction: column !important;
          align-items: stretch !important;
          justify-content: flex-start !important;
          padding: 16px;
          
          .cabin-header-horizontal {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            margin-bottom: 12px;
            
            .price-main {
              display: flex !important;
              align-items: baseline !important;
              
              .currency {
                font-size: 16px;
                color: #ff6b35;
                font-weight: 600;
              }
              
              .amount {
                font-size: 28px;
                color: #ff6b35;
                font-weight: 700;
                margin-left: 2px;
              }
              
              .note {
                font-size: 12px;
                color: #999;
                margin-left: 8px;
              }
            }
            
            .book-btn {
              .u-button {
                padding: 8px 20px !important;
                font-size: 16px !important;
                font-weight: 600 !important;
                border-radius: 20px !important;
              }
            }
          }
          
          .cabin-info-horizontal {
            display: flex !important;
            align-items: center !important;
            padding: 8px 0;
            border-bottom: 1px solid #f5f5f5;
            margin-bottom: 8px;
            
            .cabin-tag-horizontal {
              display: flex !important;
              align-items: center !important;
              gap: 6px;
              margin-right: 12px;
              
              .cabin-name {
                font-size: 14px;
                color: #333;
                font-weight: 600;
              }
              
              .discount-tag {
                background: #faad14;
                color: white;
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 4px;
                font-weight: 600;
              }
            }
            
            .cabin-services {
              display: flex !important;
              align-items: center !important;
              flex: 1;
              gap: 8px;
              
              .service-item {
                font-size: 12px;
                color: #52c41a;
                background: #f6ffed;
                border: 1px solid #b7eb8f;
                border-radius: 4px;
                padding: 2px 6px;
                white-space: nowrap;
              }
            }
          }
          

        }
      }
    }
    
    .empty-state {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
      
      .empty-text {
        font-size: 14px;
        color: #999;
      }
    }
  }
  
  /* 往返程航班信息展示样式 */
  .round-trip-info {
    background: #FFFFFF;
    margin-top: 1px;
    
    .flight-info-section {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        
        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
        
        .route-info {
          font-size: 14px;
          color: #666;
        }
        
        .selected-cabin {
          display: flex;
          align-items: center;
          gap: 4px;
          
          .selected-text {
            font-size: 12px;
            color: #52c41a;
            font-weight: 500;
          }
        }
      }
      
      .flight-details {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .flight-time {
          display: flex;
          flex-direction: column;
          align-items: center;
          
          .time {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
          }
          
          .airport {
            font-size: 12px;
            color: #666;
          }
        }
        
        .flight-duration {
          flex: 1;
          text-align: center;
          
          .flight-no {
            font-size: 14px;
            color: #333;
            font-weight: 500;
          }
        }
      }
    }
    
    .progress-tip {
      margin: 12px 16px 0;
      text-align: center;
      padding: 10px 16px;
      background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
      border: 1px solid #d1ecf1;
      border-radius: 8px;
      
      .tip-text {
        font-size: 13px;
        color: #0c5460;
      }
    }
  }
  

  
  /* 往返程舱位列表优化 */
  .round-trip-mode {
    .cabin-list {
      padding: 0;
      margin: 0;
      
      .cabin-item {
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  /* 往返程舱位列表样式 */
  .round-trip-cabins {
    .cabin-section {
      margin-bottom: 24px;
      
      .section-title {
        background: #f8f9fa;
        padding: 12px 16px;
        border-left: 4px solid #1890ff;
        
        .title-text {
          display: block;
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 4px;
        }
        
        .title-subtitle {
          font-size: 12px;
          color: #666;
        }
      }
      
      .cabin-list {
        padding: 0;
        
        .cabin-item {
          &.selected {
            border: 2px solid #52c41a;
            background: #f6ffed;
          }
        }
      }
    }
  }
  
     /* 合并舱位样式 - 简化版 */
   .combined-cabin {
     border: 1px solid #e8e8e8;
     border-radius: 8px;
     margin-bottom: 12px;
     background: #ffffff;
     padding: 16px;
     
     .cabin-header {
       display: flex;
       justify-content: space-between;
       align-items: center;
       margin-bottom: 12px;
       
       .price-main {
         display: flex;
         align-items: baseline;
         
         .currency {
           font-size: 16px;
           color: #ff6b35;
           font-weight: 600;
         }
         
         .amount {
           font-size: 28px;
           color: #ff6b35;
           font-weight: 700;
           margin-left: 2px;
         }
         
         .note {
           font-size: 12px;
           color: #999;
           margin-left: 8px;
         }
       }
       
       .book-btn {
         .u-button {
           padding: 8px 20px !important;
           font-size: 16px !important;
           font-weight: 600 !important;
           border-radius: 20px !important;
         }
       }
     }
     
     .trip-info {
       display: flex;
       align-items: center;
       padding: 8px 0;
       border-bottom: 1px solid #f5f5f5;
       
       &:last-of-type {
         border-bottom: none;
         margin-bottom: 0;
       }
       
       .trip-label {
         display: flex;
         align-items: center;
         width: 100%;
         
         .label-text {
           display: inline-flex;
           align-items: center;
           justify-content: center;
           width: 24px;
           height: 24px;
           background: #1890ff;
           color: white;
           border-radius: 50%;
           font-size: 12px;
           font-weight: 600;
           margin-right: 12px;
           flex-shrink: 0;
         }
         
         .cabin-discount {
           font-size: 16px;
           color: #333;
           font-weight: 600;
           margin-left: auto;
         }
       }
     }
   }
   
   /* 新增：往返程横向布局样式 */
   .combined-cabin-horizontal {
     border: 1px solid #e8e8e8;
     border-radius: 8px;
     margin-bottom: 12px;
     background: #ffffff;
     padding: 16px;
     /* 关键：覆盖默认的cabin-item布局 */
     flex-direction: column !important;
     align-items: stretch !important;
     justify-content: flex-start !important;
     
            .cabin-header-horizontal {
         display: flex !important;
         justify-content: space-between !important;
         align-items: center !important;
         margin-bottom: 12px;
         
         .price-main {
           display: flex !important;
           align-items: baseline !important;
           
           .currency {
             font-size: 16px;
             color: #ff6b35;
             font-weight: 600;
           }
           
           .amount {
             font-size: 28px;
             color: #ff6b35;
             font-weight: 700;
             margin-left: 2px;
           }
           
           .note {
             font-size: 12px;
             color: #999;
             margin-left: 8px;
           }
         }
       
       .book-btn {
         .u-button {
           padding: 8px 20px !important;
           font-size: 16px !important;
           font-weight: 600 !important;
           border-radius: 20px !important;
         }
       }
     }
     
     .trip-info-horizontal {
       display: flex !important;
       align-items: center !important;
       padding: 8px 0;
       border-bottom: 1px solid #f5f5f5;
       
       &:last-of-type {
         border-bottom: none;
       }
       
       .trip-item {
         display: flex !important;
         align-items: center !important;
         width: 100%;
         gap: 8px;
         
         .trip-label {
           display: inline-flex;
           align-items: center;
           justify-content: center;
           width: 24px;
           height: 24px;
           background: #1890ff;
           color: white;
           border-radius: 50%;
           font-size: 12px;
           font-weight: 600;
           flex-shrink: 0;
         }
         
         .cabin-class {
           font-size: 12px;
           color: #333;
           font-weight: 500;
         }
         
         .meal-tag {
           font-size: 11px;
           color: #52c41a;
           background: #f6ffed;
           border: 1px solid #b7eb8f;
           border-radius: 4px;
           padding: 1px 4px;
           margin-left: 8px;
           white-space: nowrap;
         }
       }
     }
     

   }
  
     /* 舱位类型选择器样式 */
   .cabin-type-selector {
     background: #FFFFFF;
     padding: 16px;
     margin: 0 0 16px 0;
    
    .selector-header {
      margin-bottom: 12px;
      
      .selector-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }
    
    .selector-tabs {
      display: flex;
      gap: 12px;
      
      .selector-tab {
        flex: 1;
        position: relative;
        padding: 16px 12px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 2px solid transparent;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &.active {
          border-color: #1890ff;
          background: #e6f7ff;
          
          .tab-label {
            color: #1890ff;
            font-weight: 600;
          }
          
          .tab-price {
            color: #1890ff;
            font-weight: 600;
          }
        }
        
        &:active {
          transform: scale(0.98);
        }
        
        .tab-label {
          display: block;
          font-size: 14px;
          font-weight: 500;
          color: #333;
          margin-bottom: 4px;
        }
        
        .tab-price {
          display: block;
          font-size: 16px;
          font-weight: 600;
          color: #1890ff;
        }
        
        .tab-tag {
          position: absolute;
          top: -2px;
          right: -2px;
          background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
          color: #ffffff;
          font-size: 10px;
          font-weight: 600;
          padding: 2px 6px;
          border-radius: 0 6px 0 6px;
          line-height: 1.2;
        }
      }
    }
  }
  
     /* 往返程舱位列表优化 */
   .round-trip-mode {
     .cabin-list {
       padding: 0;
       margin: 0;
       
       .cabin-item {
         margin-bottom: 8px;
         
         &:last-child {
           margin-bottom: 0;
         }
       }
     }
   }
 }
 </style>
