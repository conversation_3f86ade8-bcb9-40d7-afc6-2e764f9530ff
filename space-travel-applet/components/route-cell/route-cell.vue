<script>
export default {
  name: 'RouteCell',
  props: {
    cellData: {
      type: Object,
      default: () => {
        return {}
      },
    },
    showOtherInfo: {
      type: Boolean,
      default: false,
    },
    // 新增：是否显示横向舱位布局
    showHorizontalCabins: {
      type: Boolean,
      default: false, // 默认关闭，保持原有简洁布局
    },
  },
  computed: {
    // 获取最低价格（含税）- 单程航班只显示经济舱价格
    lowestPrice() {
      if (!this.cellData || !this.cellData.cabinClses || !this.cellData.cabinClses.length) {
        return 0
      }
      
      // 过滤出经济舱
      const economyCabins = this.cellData.cabinClses.filter(cabin => {
        const cabinCode = (cabin.cabinCls || cabin.cabinCode || cabin.cabin || '').toString().toUpperCase()
        return ['Y', 'M', 'H', 'K', 'L', 'Q', 'T', 'E', 'U', 'V', 'W', 'S', 'N', 'R', 'G', 'X', 'B'].includes(cabinCode)
      })
      
      // 如果没有经济舱，则显示所有舱位的最低价
      const cabinsToCheck = economyCabins.length > 0 ? economyCabins : this.cellData.cabinClses
      
      const lowestCabin = cabinsToCheck.reduce((min, cabin) => {
        // 优先使用 priceAdult，如果为空则使用 adultOrigPrice
        const totalPrice = Number(cabin.priceAdult || cabin.adultOrigPrice || 0) + Number(cabin.taxFeeAdult || 0)
        return totalPrice < min.total ? { total: totalPrice, cabin } : min
      }, { total: Infinity, cabin: null })
      
      return lowestCabin.total === Infinity ? 0 : lowestCabin.total
    },
    // 优惠金额
    couponAmount() {
      if (!this.cellData || !this.cellData.cabinClses || !this.cellData.cabinClses.length) {
        return 0
      }
      const amount = this.cellData.cabinClses[0].couponAmount || 0
      if (amount > 0) {
        console.log('🎯 检测到优惠金额:', amount, '航班:', this.cellData.flightBaseInfoVo?.flightNo)
      }
      return amount
    },
    // 产品类型
    productType() {
      if (!this.cellData || !this.cellData.cabinClses || !this.cellData.cabinClses.length) {
        return ''
      }
      const type = this.cellData.cabinClses[0].productType || ''
      if (type) {
        console.log('🏷️ 检测到产品类型:', type, '航班:', this.cellData.flightBaseInfoVo?.flightNo)
      }
      return type
    },
    
    // 新增：获取经济舱价格信息
    economyCabinInfo() {
      if (!this.cellData || !this.cellData.cabinClses || !this.cellData.cabinClses.length) {
        return null
      }
      
      const economyCabins = this.cellData.cabinClses.filter(cabin => {
        const cabinCode = (cabin.cabinCls || cabin.cabinCode || cabin.cabin || '').toString().toUpperCase()
        return ['Y', 'M', 'H', 'K', 'L', 'Q', 'T', 'E', 'U', 'V', 'W', 'S', 'N', 'R', 'G', 'X', 'B'].includes(cabinCode)
      })
      
      if (economyCabins.length === 0) return null
      
      const minPrice = Math.min(...economyCabins.map(cabin => {
        const basePrice = parseFloat(cabin.priceAdult || cabin.adultOrigPrice || 0)
        const taxFee = parseFloat(cabin.taxFeeAdult || 0)
        const oilFee = parseFloat(cabin.oilFeeAdult || 0)
        const airportFee = parseFloat(cabin.amtAdultAirPortFee || 0)
        return basePrice + oilFee + airportFee
      }).filter(price => price > 0))
      
      return {
        name: '经济舱',
        price: minPrice || 0
      }
    },
    
    // 新增：获取公务/头等舱价格信息
    businessCabinInfo() {
      if (!this.cellData || !this.cellData.cabinClses || !this.cellData.cabinClses.length) {
        return null
      }
      
      const businessCabins = this.cellData.cabinClses.filter(cabin => {
        const cabinCode = (cabin.cabinCls || cabin.cabinCode || cabin.cabin || '').toString().toUpperCase()
        return ['F', 'C', 'J', 'D', 'I', 'Z', 'P', 'A', 'O'].includes(cabinCode)
      })
      
      if (businessCabins.length === 0) return null
      
      const minPrice = Math.min(...businessCabins.map(cabin => {
        const basePrice = parseFloat(cabin.priceAdult || cabin.adultOrigPrice || 0)
        const taxFee = parseFloat(cabin.taxFeeAdult || 0)
        const oilFee = parseFloat(cabin.oilFeeAdult || 0)
        const airportFee = parseFloat(cabin.amtAdultAirPortFee || 0)
        return basePrice + oilFee + airportFee
      }).filter(price => price > 0))
      
      return {
        name: '公务/头等舱',
        price: minPrice || 0,
        hasTag: true, // 显示"超值"标签
        tagText: '超值'
      }
    }
  },
  data() {
    return {
      logoSrc: `${getApp().globalData.staticPath}/images/donghang-logo.png`,
    }
  },
  methods: {
    clickCell() {
      console.log('点击航班，传递数据:', this.cellData)
      this.$emit('on-click', this.cellData)
    },
    
    // 新增：点击舱位类型
    onCabinClick(cabinType) {
      console.log('点击舱位类型:', cabinType)
      this.$emit('on-cabin-click', {
        cabinType,
        flightData: this.cellData
      })
    },
    
    // 新增：获取产品类型标签
    getProductTypeLabel(productType) {
      const productTypeMap = {
        '青年特惠': '青年特惠',
        '老年特惠': '老年特惠', 
        '青老年特惠': '青老年特惠',
        '小团特惠': '小团特惠',
        'YOUTH_DISCOUNT': '青年特惠',
        'OLD_DISCOUNT': '老年特惠',
        'YOUTH_OLD_DISCOUNT': '青老年特惠',
        'SMALL_GROUP_DISCOUNT': '小团特惠'
      }
      return productTypeMap[productType] || productType
    }
  },
}
</script>

<template>
  <view v-if="cellData" class="route-cell-box" @click="clickCell">
    <view class="route-cell-wrap">
      <view class="info">
        <view class="info-row">
          <view class="start">
            <view class="time">
              {{ cellData.flightBaseInfoVo.deptTime ? $formatDateStr(cellData.flightBaseInfoVo.deptTime, 'hh:MM') : '' }}
            </view>
            <view class="name">
              {{ cellData.flightBaseInfoVo.deptAirportName }}{{ cellData.flightBaseInfoVo.depTerminal || '--' }}
            </view>
          </view>
          <view class="through">
            <view class="through-time">
              {{ $timeDiff(cellData.flightBaseInfoVo.deptTime, cellData.flightBaseInfoVo.arrTime) }}
            </view>
            <view class="line">
              <view class="triangle" />
            </view>
            <view class="through-name">
              {{ cellData.flightBaseInfoVo.stop ? `经停${cellData.flightBaseInfoVo.stop}` : '' }}
            </view>
          </view>
          <view class="end">
            <view class="time">
              {{ cellData.flightBaseInfoVo.arrTime ? $formatDateStr(cellData.flightBaseInfoVo.arrTime, 'hh:MM') : '' }}
            </view>
            <view class="name">
              {{ cellData.flightBaseInfoVo.arrAirportName }}{{ cellData.flightBaseInfoVo.arrTerminal || '--' }}
            </view>
          </view>
        </view>
        <view class="title">
          <u-image :src="cellData.flightBaseInfoVo.airIconUrl" :show-loading="true" width="16px" height="16px" />
          <text>{{ cellData.flightBaseInfoVo.flightNo }}</text>
          <view v-if="cellData.flightBaseInfoVo.share" class="title-share">
            共享
          </view>
        </view>
      </view>
      
      <!-- 新增：单程航班只显示经济舱价格 -->
      <view v-if="showHorizontalCabins" class="cabin-selector single-economy">
        <!-- 只显示经济舱选项 -->
        <view 
          v-if="economyCabinInfo" 
          class="cabin-option economy-only" 
          @click.stop="onCabinClick('economy')"
        >
          <text class="cabin-name">{{ economyCabinInfo.name }}</text>
          <text class="cabin-price">¥{{ Math.round(economyCabinInfo.price) }}起</text>
        </view>
      </view>
      
      <!-- 原有的纵向价格布局（作为备选） -->
      <view v-else class="detail">
        <view class="money" v-if="lowestPrice">
          <text>￥</text>
          <text>{{ lowestPrice }}</text>
        </view>
        <!-- 优惠信息显示 -->
        <view v-if="couponAmount || productType" class="promotion-info">
          <!-- 产品类型标签 -->
          <view v-if="productType" class="product-tag">
            {{ getProductTypeLabel(productType) }}
          </view>
          <!-- 优惠金额 -->
          <view v-if="couponAmount" class="coupon">
            已优惠￥{{ Number(couponAmount) }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.route-cell-box{
  padding-top: 8px;
}

.route-cell-wrap{
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  min-height: 103px;
  background: #FFFFFF;
  box-shadow: 0px 2px 6px 0px rgba(202,202,202,0.42);
  border-radius: 8px;
  overflow: hidden;

  .info{
    display: flex;
    flex-flow: column;
    justify-content: space-between;
    width: 430rpx;
    padding: 32rpx 0 32rpx 32rpx;
    height: 100%;

    .info-row{
      display: flex;
      align-items: center;

      .start,
      .end{
        width: 64px;
      }

      .through{
        width: 70px;
        margin: 0 8px;
        font-size: 12px;
        color: #999999;
        line-height: 16px;
        text-align: center;
        .through-time{
          margin-top: 22rpx;
        }
        .through-name{
          height: 14px;
          margin-top: 2px;
          font-size: 10px;
          line-height: 14px;
        }

        .line{
          position: relative;
          width: 100%;
          margin-top: 4px;
          border-bottom: 1px solid #D8D8D8;
          .triangle{
            position: absolute;
            right: -1px;
            bottom: -1px;
            height: 0px;
            width: 0px;
            border-style: solid;
            border-width:  6px 0 0 6px;
            border-color: transparent transparent transparent #D8D8D8;
          }
        }
      }

      .time{
        font-size: 20px;
        font-weight: bold;
        line-height: 24px;
      }
      .name{
        margin-top: 4px;
        font-size: 12px;
        color: #666666;
        line-height: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .title{
      display: flex;
      align-items: center;
      margin-top: 6px;
      font-size: 12px;
      color: #333333;
      line-height: 17px;
      text {
        margin-left: 3px;
      }
      &-share{
        margin-left: 16rpx;
        width: 56rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #333;
        font-size: 20rpx;
        border-radius: 4rpx;
        border: 2rpx solid rgba(0, 0, 0, 0.20);
      }
    }
  }

  // 新增：单程航班经济舱样式
  .cabin-selector {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: stretch;
    width: 280rpx;
    padding: 16rpx;
    
    &.single-economy {
      // 单个经济舱选项时的样式调整
      padding: 8rpx 16rpx;
    }
    
    .cabin-option {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12rpx 16rpx;
      background: #f8f9fa;
      border-radius: 8rpx;
      border: 1px solid #e9ecef;
      transition: all 0.2s ease;
      cursor: pointer;
      
      &:active {
        transform: scale(0.98);
        background: #e9ecef;
      }
      
      &.economy-only {
        // 单独经济舱的样式优化
        background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
        border-color: #91d5ff;
        
        &:active {
          background: linear-gradient(135deg, #d4edda 0%, #e6f7ff 100%);
        }
        
        .cabin-name {
          color: #1890ff;
          font-weight: 600;
        }
        
        .cabin-price {
          color: #f5222d;
          font-weight: 700;
          font-size: 16px;
        }
      }
      
      .cabin-name {
        font-size: 12px;
        color: #666;
        font-weight: 500;
        text-align: center;
        margin-bottom: 4rpx;
      }
      
      .cabin-price {
        font-size: 14px;
        color: #1890ff;
        font-weight: 600;
        text-align: center;
      }
    }
  }
  
  // 原有的纵向价格布局样式
  .detail {
    position: relative;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    flex-flow: column;
    width: 256rpx;
    height: 100%;
    padding: 32rpx 32rpx 32rpx 0;
    text-align: center;

    .money{
      font-weight: bold;
      text:first-child {
        font-size: 12px;
        color: #F5222D;
        line-height: 17px;
      }
      text:last-child {
        font-size: 44rpx;
        color: #F5222D;
        line-height: 48rpx;
      }

    }
    
    // 优惠信息容器
    .promotion-info {
      margin-top: 16rpx;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8rpx;
    }
    
    // 产品类型标签
    .product-tag {
      background: linear-gradient(90deg, #ff6b35 0%, #f7931e 100%);
      color: #ffffff;
      font-size: 20rpx;
      font-weight: 500;
      padding: 4rpx 8rpx;
      border-radius: 8rpx;
      text-align: center;
      box-shadow: 0 2rpx 4rpx rgba(255, 107, 53, 0.3);
    }
    
    .coupon{
      background: linear-gradient( 90deg, rgba(254,144,104,0) 0%, rgba(245,34,45,0.15) 100%);
      border-radius: 4rpx;
      font-weight: 400;
      font-size: 20rpx;
      color: #F5222D;
      line-height: 32rpx;
      padding: 4rpx 8rpx;
    }
    .name {
      margin-top: 2px;
      font-size: 10px;
      color: #666666;
      line-height: 14px;
    }
  }
}
</style>
