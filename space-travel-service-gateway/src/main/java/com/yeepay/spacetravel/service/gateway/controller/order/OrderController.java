package com.yeepay.spacetravel.service.gateway.controller.order;
import com.yeepay.g3.utils.common.BeanUtils;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.spacetravel.common.util.exception.ReturnCode;
import com.yeepay.spacetravel.service.facade.dto.order.request.*;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.spacetravel.service.facade.dto.order.response.OrderPagePaySuccessResponseDTO;
import com.yeepay.spacetravel.service.facade.dto.order.response.TicketOrderResponseDTO;
import com.yeepay.spacetravel.service.facade.facade.order.OrderFacade;
import com.yeepay.spacetravel.service.facade.result.BaseResult;
import com.yeepay.spacetravel.service.gateway.base.BaseController;
import com.yeepay.spacetravel.service.gateway.util.net.NetUtils;
import com.yeepay.spacetravel.service.gateway.vo.order.OrderPagePaySuccessParam;
import com.yeepay.spacetravel.service.gateway.vo.order.OrderPagePaySuccessResponseVo;
import com.yeepay.spacetravel.service.gateway.vo.order.TicketOrderPassengerRequestParam;
import com.yeepay.spacetravel.service.gateway.vo.order.TicketOrderRequestParam;
import com.yeepay.spacetravel.service.gateway.vo.order.TicketOrderResponseVo;
import com.yeepay.spacetravel.service.gateway.vo.queryOrder.OrderInfoResponseVo;
import com.yeepay.yop.sdk.utils.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: qingwang.gu
 * @Date: 2022/11/10 15:34
 * @Description:
 */
@RestController
@RequestMapping("/order")
@Api(tags = {"订单"})
public class OrderController extends BaseController {
    private Logger logger = LoggerFactory.getLogger(OrderController.class);

    @RequestMapping(value = "/Reservation",method = RequestMethod.POST)
    @ApiOperation(value = "下单",notes = "下单")
    public BaseResult<TicketOrderResponseVo> reservation(@RequestBody TicketOrderRequestParam param, HttpServletRequest request){
        logger.info("用户请求下单参数{}", JsonUtils.toJsonString(param));
        try{
            String memberId = getMemberId();
            TicketOrderRequestDTO dto = new TicketOrderRequestDTO();
            dto.setTicketTotalAmount(param.getTicketTotalAmount());
            dto.setMemberId(memberId);
            TicketOrderFlightRequestDTO flightDto = new TicketOrderFlightRequestDTO();
            BeanUtils.copyProperties(param.getFlight(),flightDto);
            dto.setFlight(flightDto);
            List<TicketOrderPassengerRequestDTO> passengerRequestDTOList = new ArrayList<>();
            List<TicketOrderPassengerRequestParam> passengerRequestParams = param.getPassengers();

            for (TicketOrderPassengerRequestParam requestParam : passengerRequestParams) {
                TicketOrderPassengerRequestDTO ticketOrderPassengerRequestDTO = new TicketOrderPassengerRequestDTO();
                BeanUtils.copyProperties(requestParam, ticketOrderPassengerRequestDTO);
                ticketOrderPassengerRequestDTO.setName(requestParam.getFirstName() + requestParam.getLastName());
                passengerRequestDTOList.add(ticketOrderPassengerRequestDTO);
            }

            dto.setPassagers(passengerRequestDTOList);
            TicketOrderContactRequestDTO contact = new TicketOrderContactRequestDTO();
            BeanUtils.copyProperties(param.getContact(),contact);
            dto.setContact(contact);

            String userIp = NetUtils.getClientIp(request);
            dto.setUserIp(userIp);
            OrderFacade facade = RemoteServiceFactory.getService(OrderFacade.class,120000);
            BaseResult<TicketOrderResponseDTO> baseResult = facade.order(dto);
            if(!baseResult.isSuccess()){
                return BaseResult.fail(baseResult.getReturnCode(),baseResult.getReturnMessage());
            }
            TicketOrderResponseDTO responseDTO = baseResult.getData();
            if(!CheckUtils.isEmpty(responseDTO)){
                TicketOrderResponseVo vo = new TicketOrderResponseVo();
                BeanUtils.copyProperties(responseDTO,vo);
                return BaseResult.success(vo);
            }
            return BaseResult.success();
        }catch (Exception e){
            logger.error("调用下单接口出现未知异常",e);
            return BaseResult.fail(ReturnCode.FAIL,"系统开小差请稍后再试");
        }
    }

    @RequestMapping(value = "/pagePaySuccess",method = RequestMethod.POST)
    @ApiOperation(value = "前端支付成功",notes = "前端支付成功")
    public BaseResult<OrderPagePaySuccessResponseVo> pagePaySuccess(@RequestBody OrderPagePaySuccessParam param){
        logger.info("前端支付成功请求参数{}",JsonUtils.toJsonString(param));
        OrderPagePaySuccessRequestDTO requestDTO = new OrderPagePaySuccessRequestDTO();
        requestDTO.setOrderId(param.getOrderId());
        requestDTO.setMemberId(getMemberId());
        OrderFacade facade = RemoteServiceFactory.getService(OrderFacade.class);
        BaseResult<OrderPagePaySuccessResponseDTO> baseResult = facade.paySuccess(requestDTO);
        logger.info("请求前端支付成功返回结果为{}",JsonUtils.toJsonString(baseResult));
        if(!baseResult.isSuccess()){
            return BaseResult.fail(baseResult.getReturnCode(),baseResult.getReturnMessage());
        }

        if(!CheckUtils.isEmpty(baseResult.getData())){
            OrderPagePaySuccessResponseVo vo = new OrderPagePaySuccessResponseVo();
            OrderPagePaySuccessResponseDTO dto = baseResult.getData();
            OrderInfoResponseVo order = new OrderInfoResponseVo();
            order.setId(dto.getOrderId());
            order.setStatus(dto.getStatus());
            order = setStatusDesc(order);
            vo.setOrderId(order.getId());
            vo.setStatus(order.getStatus());
            vo.setStatusDes(order.getStatusDesc());
            return BaseResult.success(vo);
        }else{
            return BaseResult.fail(ReturnCode.FAIL,ReturnCode.getRetMsg(ReturnCode.FAIL));
        }

    }
}
